using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Solacom.InfoHub.EventReceiver.BusinessLogic.Enums;
using Solacom.InfoHub.EventReceiver.BusinessLogic.Interfaces;
using Solacom.InfoHub.EventReceiver.ElasticSearch.Entities;
using Solacom.InfoHub.EventReceiver.Exceptions;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Interfaces;
using Solacom.InfoHub.EventReceiver.MariaDb.Context.Models;
using EventLog = Solacom.InfoHub.EventReceiver.Entities.EventLog;
using Solacom.InfoHub.EventReceiver.BusinessLogic.ALI;
using Microsoft.Extensions.Options;
using Solacom.InfoHub.EventReceiver.Entities;
using Microsoft.Extensions.Caching.Memory;

namespace Solacom.InfoHub.EventReceiver.BusinessLogic
{
    public class EventsManager : IEventsManager
    {
        private readonly Classofservice _cosSettings;
        private readonly ILogger<EventsManager> _logger;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IUnitOfWork_InsightsData _unitOfWork_InsightsData;
        private readonly IMemoryCache _cache;
        public EventsManager(IUnitOfWork unitOfWork, IUnitOfWork_InsightsData unitOfWork_InsightsData, IOptions<Classofservice> settings, ILogger<EventsManager> logger, IMemoryCache cache)
        {
            _unitOfWork = unitOfWork;
            _unitOfWork_InsightsData = unitOfWork_InsightsData;
            _cosSettings = settings.Value;
            _logger = logger;
            _cache = cache;
        }
                        
        /// <summary>
        /// 
        /// </summary>
        /// <param name="eventDataXml">The full raw XML object represting the Event Log data</param>
        /// <param name="eventLog">The Serialized eventLog object based on the Event data XML</param>
        /// <param name="userKeyData">should be string clientCode</param>
        /// <returns></returns>
        /// <remarks>userKeyData can be simplified to be just ClientCode - it is the only property that is used from this entry onwards</remarks>
        public async Task<SaveEventResult> SaveEvent(string eventDataXml, EventLog eventLog, UserKeyData userKeyData)
        {
            var response = new SaveEventResult();
            bool repeatEvent = false;
            bool eventAdded = false;
            bool ignoreEvent = false;

            Eventhash eventHash = new Eventhash
            {
                Clientcode = userKeyData.ClientCode,
                Content = eventDataXml,
                Eventreceived = DateTime.UtcNow
            };

            try
            {
                var hashedEvent = new HashingHelper().Hash(eventDataXml);

                eventHash.Hashedcontent = hashedEvent;
                eventHash.EventType = eventLog.eventType;

                if (string.IsNullOrEmpty(eventDataXml))
                {
                    throw new InvalidParemeterException("Event data is null");
                }

                var prevEvent = await _unitOfWork.GetHashEvent(hashedEvent, userKeyData.ClientCode);

                if (prevEvent != null)
                {
                    repeatEvent = true;
                    //already seen this event and last push was success
                    response.Messages.Add($"{prevEvent.Callid}: Event already received at: {prevEvent.Eventreceived}");
                    _logger.LogInformation($"{prevEvent.Callid}: Event already received at: {prevEvent.Eventreceived}");

                    eventHash.Id = prevEvent.Id;
                    response.IsSuccess = true;
                    return response;
                }

                eventHash.Callid = eventLog.callIdentifier;
                eventHash.Eventtimestamp = eventLog.timestamp;

                bool isSupportedEventType = Enum.IsDefined(typeof(EventType), eventLog.eventType);
                bool isSupportAgentAuditType = Enum.IsDefined(typeof(EventType_AgentAudit), eventLog.eventType);
                bool isSupportedEventTypeNonSerialized = Enum.IsDefined(typeof(EventType_NonSerialized), eventLog.eventType);

                if (!isSupportedEventType &&
                    !isSupportAgentAuditType &&
                    !isSupportedEventTypeNonSerialized)
                {
                    response.IsSuccess = true;
                    _logger.LogInformation($"{eventLog.eventType} is not a supported Event Type.  No processing actions are performed");
                    return response;
                }

				//New logic - do not process / story the CDRType1 event UNLESS next gen.
                //If not next gen - and CDRType1 - no logic required/no additional actions taken.
                if(!userKeyData.ClientEsiNETSupported && eventLog.cdrType1 != null)
                {
                    //Do not add the cdr event in the finally clause - ignoring it completely.
                    ignoreEvent = true;
                    response.IsSuccess = true;
                    return response;
                }
                switch (eventLog.eventType)
                {
                    case "StartCall":
                    case "OutboundCall":
                    {
                            //// Commented out to avoid Call Summary writting when the PSAP isn't available yet. (part of https://solacomtech.atlassian.net/browse/INFO-1192)
                            /// Later logic stores the Root data back into the DB, which is what the Root event is based upon (see above GetExistingEventRoot call)
                            /// Insert to the ES index occurs in the Process call 

                            //if (!existingEventRoot.IsESRootExists)
                            //{
                            //        InsertESRootCallSummary(eventLog, userKeyData.IndexPrefix, userKeyData.TenantLookup);
                            //        existingEventRoot.IsESRootExists = true;
                            //}
                            break;
                    }

                    case "Login":
                    {
                        if (!string.IsNullOrEmpty(eventLog.login.DeviceName) &&
                            eventLog.login.DeviceName.ToLower() != "headset"
                            || string.IsNullOrEmpty(eventLog.login.MediaLabel) || eventLog.login.MediaLabel.Equals("."))
                            break;
                        var existingAgentSessionRecord = await FindAgentSessionRecord(eventLog.login.MediaLabel, userKeyData);
                        if (existingAgentSessionRecord != null)
                        {
                            var elkExistingAuditRecords = existingAgentSessionRecord.FindAgentAudit(existingAgentSessionRecord.CloudId);
                            if (elkExistingAuditRecords == null)
                            {
                                AgentAudit _newAgentaudit = new AgentAudit
                                {
                                    MediaLabel = existingAgentSessionRecord.MediaLabel,
                                    Logintime = existingAgentSessionRecord.Logintime,
                                    TimeStamp = existingAgentSessionRecord.TimeStamp,
                                    isOnDuty = existingAgentSessionRecord.isOnDuty,
                                    isAvailable = existingAgentSessionRecord.isAvailable,
                                    PsapName = existingAgentSessionRecord.PsapName,
                                    AgentName = existingAgentSessionRecord.AgentName,
                                    Uri = existingAgentSessionRecord.Uri,
                                    AgentRole = existingAgentSessionRecord.AgentRole,
                                    TenantGroup = existingAgentSessionRecord.TenantGroup,
                                    OperatorId = existingAgentSessionRecord.OperatorId,
                                    Workstation = existingAgentSessionRecord.Workstation,
                                    Devicename = existingAgentSessionRecord.Devicename,
                                    Reason = existingAgentSessionRecord.Reason,
                                    Id = existingAgentSessionRecord.CloudId
                                };
                                existingAgentSessionRecord.AddAgentAudit(_newAgentaudit);
                                await _unitOfWork.UpdateAgentSession(existingAgentSessionRecord, userKeyData.ClientCode);
                            }
                        }
                        else
                        {
                            AgentSessionRecord newSessionRecord = new AgentSessionRecord
                            {
                                MediaLabel = eventLog.login.MediaLabel,
                                Logintime = eventLog.timestamp,
                                TimeStamp = eventLog.timestamp,
                                isOnDuty = 1,
                                isAvailable = 0,
                                PsapName = eventLog.agencyOrElement,
                                AgentName = eventLog.agent,
                                Uri = eventLog.login.Uri,
                                AgentRole = eventLog.login.AgentRole,
                                TenantGroup = eventLog.login.TenantGroup,
                                OperatorId = eventLog.login.OperatorId,
                                Workstation = eventLog.login.Workstation,
                                Devicename = eventLog.login.DeviceName,
                                Reason = eventLog.login.Reason,
                                CloudId = Guid.NewGuid()
                            };
                            
                            AgentAudit _newAgentaudit = new AgentAudit() {
                                MediaLabel = eventLog.login.MediaLabel,
                                Logintime = eventLog.timestamp,
                                TimeStamp = newSessionRecord.TimeStamp,
                                isOnDuty = 1,
                                isAvailable = 0, //TBD
                                PsapName = eventLog.agencyOrElement,
                                AgentName = eventLog.agent,
                                Uri = eventLog.login.Uri,
                                AgentRole = eventLog.login.AgentRole,
                                TenantGroup = eventLog.login.TenantGroup,
                                OperatorId = eventLog.login.OperatorId,
                                Workstation = eventLog.login.Workstation,
                                Devicename = eventLog.login.DeviceName,
                                Reason = eventLog.login.Reason,
                                Id = newSessionRecord.CloudId
                            };
                            newSessionRecord.AddAgentAudit(_newAgentaudit);
                            await _unitOfWork.UpdateAgentSession(newSessionRecord, userKeyData.ClientCode);
                            }

                        break;
                    }

                    case "Logout":
                    {
                        if (string.IsNullOrEmpty(eventLog.logout.DeviceName) || eventLog.logout.DeviceName.ToLower() !=
                                                                             "headset"
                                                                             || string.IsNullOrEmpty(eventLog.logout
                                                                                 .MediaLabel) ||
                                                                             eventLog.logout.MediaLabel.Equals("."))
                            break;
                        var existingAgentSessionRecord = await FindAgentSessionRecord(eventLog.logout.MediaLabel, userKeyData);

                        if (existingAgentSessionRecord != null)
                        {
                            //Build the busied out and available list entries for ELK and also update the tables agentbusiedrecord and agentavailable record in Maria DB if required
                            var existingAgentBusiedRecord = existingAgentSessionRecord.AgentBusiedRecords.ToList();
                            var existingAgentAvailableRecord = existingAgentSessionRecord.AgentAvailableRecords.ToList();

                            float totalBusiedMinutes = 0, totalAvailableMinutes = 0;
                            List<AvailableIntervalLog> listAgentAvailable = new List<AvailableIntervalLog>();
                            List<BusiedoutIntervalLog> listAgentBusiedOut = new List<BusiedoutIntervalLog>();
                            foreach (var existingBusiedRecord in existingAgentBusiedRecord)
                            {

                                if (existingBusiedRecord.StartBusyInterval != null &&
                                    existingBusiedRecord.EndBusyInterval != null)
                                {
                                    if (existingBusiedRecord.IntervalTimeInMinutes == null)
                                    {
                                        existingBusiedRecord.IntervalTimeInMinutes = (float) existingBusiedRecord
                                            .EndBusyInterval.GetValueOrDefault()
                                            .Subtract(existingBusiedRecord.StartBusyInterval.GetValueOrDefault())
                                            .TotalMinutes;
                                    }

                                    listAgentBusiedOut.Add(new BusiedoutIntervalLog
                                    {
                                        StartBusyInterval = existingBusiedRecord.StartBusyInterval,
                                        EndBusyInterval = existingBusiedRecord.EndBusyInterval,
                                        IntervalTimeInMinutes = existingBusiedRecord.IntervalTimeInMinutes,
                                        BusiedOutAction = existingBusiedRecord.BusiedOutAction
                                    });
                                    totalBusiedMinutes +=
                                        existingBusiedRecord.IntervalTimeInMinutes.GetValueOrDefault();
                                }
                                else if (existingBusiedRecord.StartBusyInterval != null &&
                                         existingBusiedRecord.EndBusyInterval == null)
                                {
                                    existingBusiedRecord.EndBusyInterval = eventLog.timestamp;
                                    existingBusiedRecord.IntervalTimeInMinutes = (float) existingBusiedRecord
                                        .EndBusyInterval.GetValueOrDefault()
                                        .Subtract(existingBusiedRecord.StartBusyInterval.GetValueOrDefault())
                                        .TotalMinutes;

                                    listAgentBusiedOut.Add(new BusiedoutIntervalLog
                                    {
                                        StartBusyInterval = existingBusiedRecord.StartBusyInterval,
                                        EndBusyInterval = existingBusiedRecord.EndBusyInterval,
                                        IntervalTimeInMinutes = existingBusiedRecord.IntervalTimeInMinutes,
                                        BusiedOutAction = existingBusiedRecord.BusiedOutAction
                                    });
                                    totalBusiedMinutes +=
                                        existingBusiedRecord.IntervalTimeInMinutes.GetValueOrDefault();
                                }
                            }

                            foreach (var existingAvailableRecord in existingAgentAvailableRecord)
                            {
                                if (existingAvailableRecord.StartAvailableInterval != null &&
                                    existingAvailableRecord.EndAvailableInterval != null)
                                {
                                    if (existingAvailableRecord.IntervalTimeInMinutes == null)
                                    {
                                        existingAvailableRecord.IntervalTimeInMinutes = (float) existingAvailableRecord
                                            .EndAvailableInterval.GetValueOrDefault()
                                            .Subtract(
                                                existingAvailableRecord.StartAvailableInterval.GetValueOrDefault())
                                            .TotalMinutes;
                                    }

                                    listAgentAvailable.Add(new AvailableIntervalLog
                                    {
                                        StartAvailableInterval = existingAvailableRecord.StartAvailableInterval,
                                        EndAvailableInterval = existingAvailableRecord.EndAvailableInterval,
                                        IntervalTimeInMinutes = existingAvailableRecord.IntervalTimeInMinutes
                                    });
                                    totalAvailableMinutes +=
                                        existingAvailableRecord.IntervalTimeInMinutes.GetValueOrDefault();
                                }
                                else if (existingAvailableRecord.StartAvailableInterval != null &&
                                         existingAvailableRecord.EndAvailableInterval == null)
                                {
                                    existingAvailableRecord.EndAvailableInterval =
                                        eventLog.timestamp;
                                    existingAvailableRecord.IntervalTimeInMinutes = (float) existingAvailableRecord
                                        .EndAvailableInterval.GetValueOrDefault()
                                        .Subtract(existingAvailableRecord.StartAvailableInterval.GetValueOrDefault())
                                        .TotalMinutes;

                                    listAgentAvailable.Add(new AvailableIntervalLog
                                    {
                                        StartAvailableInterval = existingAvailableRecord.StartAvailableInterval,
                                        EndAvailableInterval = existingAvailableRecord.EndAvailableInterval,
                                        IntervalTimeInMinutes = existingAvailableRecord.IntervalTimeInMinutes
                                    });
                                    totalAvailableMinutes +=
                                        existingAvailableRecord.IntervalTimeInMinutes.GetValueOrDefault();
                                }
                            }

                            //Update the Maria DB record for agent session-update the logout time and isAvailable, isOnDuty flags, availableminutes, busiedoutminutes, dutytimeminutes
                            existingAgentSessionRecord.Logouttime =
                                eventLog.timestamp;
                            existingAgentSessionRecord.isOnDuty = 0;
                            existingAgentSessionRecord.isAvailable = 0;
                            existingAgentSessionRecord.dutyTimeMinutes =
                                (float) existingAgentSessionRecord.Logouttime.GetValueOrDefault()
                                    .Subtract(existingAgentSessionRecord.Logintime.GetValueOrDefault())
                                    .TotalMinutes;
                            existingAgentSessionRecord.availableTimeMinutes = totalAvailableMinutes;
                            existingAgentSessionRecord.busiedOutTimeMinutes = totalBusiedMinutes;
                            

                            var elkExistingAuditRecords = existingAgentSessionRecord.FindAgentAudit(existingAgentSessionRecord.CloudId);
                                if (elkExistingAuditRecords == null)
                            {
                                AgentAudit _newAgentAudit = new AgentAudit() {
                                    AvailableIntervalLogs = listAgentAvailable,
                                    BusiedoutIntervalLogs = listAgentBusiedOut,
                                    MediaLabel = existingAgentSessionRecord.MediaLabel,
                                    Logintime = existingAgentSessionRecord.Logintime,
                                    TimeStamp = existingAgentSessionRecord.TimeStamp,
                                    isOnDuty = existingAgentSessionRecord.isOnDuty,
                                    isAvailable = existingAgentSessionRecord.isAvailable,
                                    PsapName = existingAgentSessionRecord.PsapName,
                                    AgentName = existingAgentSessionRecord.AgentName,
                                    Uri = existingAgentSessionRecord.Uri,
                                    AgentRole = existingAgentSessionRecord.AgentRole,
                                    TenantGroup = existingAgentSessionRecord.TenantGroup,
                                    OperatorId = existingAgentSessionRecord.OperatorId,
                                    Workstation = existingAgentSessionRecord.Workstation,
                                    Devicename = existingAgentSessionRecord.Devicename,
                                    Reason = existingAgentSessionRecord.Reason,
                                    Id = existingAgentSessionRecord.CloudId
                                };
                                existingAgentSessionRecord.AddAgentAudit(_newAgentAudit);
                                await _unitOfWork.UpdateAgentSession(existingAgentSessionRecord, userKeyData.ClientCode);
                            }
                            else
                            {
                                //Update all the required attributes in the agent session ELK record
                                elkExistingAuditRecords.BusiedoutIntervalLogs = listAgentBusiedOut;
                                elkExistingAuditRecords.AvailableIntervalLogs = listAgentAvailable;
                                elkExistingAuditRecords.Logouttime = eventLog.timestamp;
                                elkExistingAuditRecords.isOnDuty = 0;
                                elkExistingAuditRecords.isAvailable = 0;
                                elkExistingAuditRecords.dutyTimeMinutes = (float) elkExistingAuditRecords.Logouttime
                                    .GetValueOrDefault().Subtract(elkExistingAuditRecords.Logintime.GetValueOrDefault())
                                    .TotalMinutes;
                                elkExistingAuditRecords.availableTimeMinutes = totalAvailableMinutes;
                                elkExistingAuditRecords.busiedOutTimeMinutes = totalBusiedMinutes;
                                existingAgentSessionRecord.UpdateAgentAudit(elkExistingAuditRecords);
                                await _unitOfWork.UpdateAgentSession(existingAgentSessionRecord, userKeyData.ClientCode);
                                }

                        }
                        else
                        {
                            AgentSessionRecord newSessionRecord = new AgentSessionRecord
                            {
                                MediaLabel = eventLog.logout.MediaLabel,
                                Logouttime = eventLog.timestamp,
                                TimeStamp = eventLog.timestamp,
                                isOnDuty = 0,
                                isAvailable = 0,
                                PsapName = eventLog.agencyOrElement,
                                AgentName = eventLog.agent,
                                Uri = eventLog.logout.Uri,
                                AgentRole = eventLog.logout.AgentRole,
                                TenantGroup = eventLog.logout.TenantGroup,
                                OperatorId = eventLog.logout.OperatorId,
                                Workstation = eventLog.logout.Workstation,
                                Devicename = eventLog.logout.DeviceName,
                                Reason = eventLog.logout.Reason,
                                CloudId = Guid.NewGuid()
                            };
                            
                            AgentAudit _newAgentaudit = new AgentAudit() {
                                MediaLabel = eventLog.logout.MediaLabel,
                                Logouttime = eventLog.timestamp,
                                TimeStamp = newSessionRecord.TimeStamp,
                                isOnDuty = 0,
                                isAvailable = 0, //TBD
                                PsapName = eventLog.agencyOrElement,
                                AgentName = eventLog.agent,
                                Uri = eventLog.logout.Uri,
                                AgentRole = eventLog.logout.AgentRole,
                                TenantGroup = eventLog.logout.TenantGroup,
                                OperatorId = eventLog.logout.OperatorId,
                                Workstation = eventLog.logout.Workstation,
                                Devicename = eventLog.logout.DeviceName,
                                Reason = eventLog.logout.Reason,
                                Id = newSessionRecord.CloudId
                            };
                            newSessionRecord.AddAgentAudit(_newAgentaudit);
                            await _unitOfWork.UpdateAgentSession(newSessionRecord, userKeyData.ClientCode);
                            }

                        break;
                    }

                    case "AgentAvailable":
                    {
                        if (string.IsNullOrEmpty(eventLog.agentAvailable?.MediaLabel) ||
                            eventLog.agentAvailable.MediaLabel.Equals("."))
                            break;
                        var existingAgentSessionRecord = await FindAgentSessionRecord(eventLog.agentAvailable.MediaLabel, userKeyData);

                        if (existingAgentSessionRecord != null)
                        {
                            var existingAgentBusiedRecord = existingAgentSessionRecord.AgentBusiedRecords.ToList();
                            var existingAgentAvailableRecord = existingAgentSessionRecord.AgentAvailableRecords.ToList();
                            //Build the busied out and available list entries for ELK and also update the tables agentbusiedrecord and agentavailable record in Maria DB if required
                            float totalBusiedMinutes = 0, totalAvailableMinutes = 0;
                            List<AvailableIntervalLog> listAgentAvailable = new List<AvailableIntervalLog>();
                            List<BusiedoutIntervalLog> listAgentBusiedOut = new List<BusiedoutIntervalLog>();
                            bool isnewAgentAvailableRecord = true;
                            foreach (var existingBusiedRecord in existingAgentBusiedRecord)
                            {
                                if (existingBusiedRecord.StartBusyInterval != null &&
                                    existingBusiedRecord.EndBusyInterval != null)
                                {
                                    if (existingBusiedRecord.IntervalTimeInMinutes <= 0)
                                    {
                                        existingBusiedRecord.IntervalTimeInMinutes = (float) existingBusiedRecord
                                            .EndBusyInterval.GetValueOrDefault()
                                            .Subtract(existingBusiedRecord.StartBusyInterval.GetValueOrDefault())
                                            .TotalMinutes;
                                    }

                                    listAgentBusiedOut.Add(new BusiedoutIntervalLog
                                    {
                                        StartBusyInterval = existingBusiedRecord.StartBusyInterval,
                                        EndBusyInterval = existingBusiedRecord.EndBusyInterval,
                                        IntervalTimeInMinutes = existingBusiedRecord.IntervalTimeInMinutes,
                                        BusiedOutAction = existingBusiedRecord.BusiedOutAction
                                    });
                                    totalBusiedMinutes +=
                                        existingBusiedRecord.IntervalTimeInMinutes.GetValueOrDefault();
                                }
                                else if (existingBusiedRecord.StartBusyInterval != null &&
                                         existingBusiedRecord.EndBusyInterval == null)
                                {
                                    existingBusiedRecord.EndBusyInterval = eventLog.timestamp;
                                    existingBusiedRecord.IntervalTimeInMinutes = (float) existingBusiedRecord
                                        .EndBusyInterval.GetValueOrDefault()
                                        .Subtract(existingBusiedRecord.StartBusyInterval.GetValueOrDefault())
                                        .TotalMinutes;
                                    
                                    listAgentBusiedOut.Add(new BusiedoutIntervalLog
                                    {
                                        StartBusyInterval = existingBusiedRecord.StartBusyInterval,
                                        EndBusyInterval = existingBusiedRecord.EndBusyInterval,
                                        IntervalTimeInMinutes = existingBusiedRecord.IntervalTimeInMinutes,
                                        BusiedOutAction = existingBusiedRecord.BusiedOutAction
                                    });
                                    totalBusiedMinutes +=
                                        existingBusiedRecord.IntervalTimeInMinutes.GetValueOrDefault();
                                }
                            }

                            foreach (var existingAvailableRecord in existingAgentAvailableRecord)
                            {
                                if (existingAvailableRecord.StartAvailableInterval != null &&
                                    existingAvailableRecord.EndAvailableInterval != null)
                                {
                                    if (existingAvailableRecord.IntervalTimeInMinutes == null)
                                    {
                                        existingAvailableRecord.IntervalTimeInMinutes = (float) existingAvailableRecord
                                            .EndAvailableInterval.GetValueOrDefault()
                                            .Subtract(
                                                existingAvailableRecord.StartAvailableInterval.GetValueOrDefault())
                                            .TotalMinutes;
                                    }

                                    listAgentAvailable.Add(new AvailableIntervalLog
                                    {
                                        StartAvailableInterval = existingAvailableRecord.StartAvailableInterval,
                                        EndAvailableInterval = existingAvailableRecord.EndAvailableInterval,
                                        IntervalTimeInMinutes = existingAvailableRecord.IntervalTimeInMinutes
                                    });
                                    totalAvailableMinutes +=
                                        existingAvailableRecord.IntervalTimeInMinutes.GetValueOrDefault();
                                }
                                else if (existingAvailableRecord.StartAvailableInterval != null &&
                                         existingAvailableRecord.EndAvailableInterval == null)
                                {
                                    isnewAgentAvailableRecord = false;
                                    /*//Review if 2 Agentavailables received without ending the previous one
                                    //Review if we want to create another empty record for available if one already exists or update this one

                                    existingAvailableRecord.EndAvailableInterval = eventLog.timestamp.GetValueOrDefault();
                                    existingAvailableRecord.IntervalTimeInMinutes = (float)existingAvailableRecord.EndAvailableInterval.Subtract(existingAvailableRecord.StartAvailableInterval).TotalMinutes;
                                    await _agentAvailableRepository.Update(existingAvailableRecord);
                                    await _agentAvailableRepository.Commit();
                                    listAgentAvailable.Add(new AvailableIntervalLog
                                    {
                                        StartAvailableInterval = existingAvailableRecord.StartAvailableInterval,
                                        EndAvailableInterval = existingAvailableRecord.EndAvailableInterval,
                                        IntervalTimeInMinutes = existingAvailableRecord.IntervalTimeInMinutes
                                    });
                                    totalAvailableMinutes += existingAvailableRecord.IntervalTimeInMinutes.GetValueOrDefault();*/
                                }
                            }

                            if (!existingAgentAvailableRecord.Any() || isnewAgentAvailableRecord)
                            {
                                existingAgentSessionRecord.AgentAvailableRecords.Add(new AgentAvailableRecord
                                {
                                    StartAvailableInterval = eventLog.timestamp
                                });
                                
                            }

                            //Update the Maria DB record for agent session-update the busiedoutminutes and isAvailable flags
                            existingAgentSessionRecord.isAvailable = 1;
                            existingAgentSessionRecord.availableTimeMinutes = totalAvailableMinutes;
                            existingAgentSessionRecord.busiedOutTimeMinutes = totalBusiedMinutes;

                            var elkExistingAuditRecords = existingAgentSessionRecord.FindAgentAudit(existingAgentSessionRecord.CloudId);
                                if (elkExistingAuditRecords == null)
                            {
                                AgentAudit _newAgentaudit = new AgentAudit() {
                                    AvailableIntervalLogs = listAgentAvailable,
                                    BusiedoutIntervalLogs = listAgentBusiedOut,
                                    MediaLabel = existingAgentSessionRecord.MediaLabel,
                                    Logintime = existingAgentSessionRecord.Logintime,
                                    TimeStamp = existingAgentSessionRecord.TimeStamp,
                                    isOnDuty = existingAgentSessionRecord.isOnDuty,
                                    isAvailable = existingAgentSessionRecord.isAvailable,
                                    PsapName = existingAgentSessionRecord.PsapName,
                                    AgentName = existingAgentSessionRecord.AgentName,
                                    Uri = existingAgentSessionRecord.Uri,
                                    AgentRole = existingAgentSessionRecord.AgentRole,
                                    TenantGroup = existingAgentSessionRecord.TenantGroup,
                                    OperatorId = existingAgentSessionRecord.OperatorId,
                                    Workstation = existingAgentSessionRecord.Workstation,
                                    Devicename = existingAgentSessionRecord.Devicename,
                                    Reason = existingAgentSessionRecord.Reason,
                                    Id = existingAgentSessionRecord.CloudId
                                };
                                existingAgentSessionRecord.AddAgentAudit(_newAgentaudit);
                                await _unitOfWork.UpdateAgentSession(existingAgentSessionRecord, userKeyData.ClientCode);
                            }
                            else
                            {
                                //Update all the required attributes in the agent session ELK record
                                elkExistingAuditRecords.BusiedoutIntervalLogs = listAgentBusiedOut;
                                elkExistingAuditRecords.AvailableIntervalLogs = listAgentAvailable;
                                elkExistingAuditRecords.isAvailable = 1;
                                elkExistingAuditRecords.availableTimeMinutes = totalAvailableMinutes;
                                elkExistingAuditRecords.busiedOutTimeMinutes = totalBusiedMinutes;
                                existingAgentSessionRecord.UpdateAgentAudit(elkExistingAuditRecords);
                                await _unitOfWork.UpdateAgentSession(existingAgentSessionRecord, userKeyData.ClientCode);
                            }
                        }
                        else
                        {
                            AgentSessionRecord newSessionRecord = new AgentSessionRecord
                            {
                                MediaLabel = eventLog.agentAvailable.MediaLabel,
                                Logintime = eventLog.timestamp, //Do we consider this as a login time?
                                TimeStamp = eventLog.timestamp,
                                isOnDuty = 1,
                                isAvailable = 1,
                                PsapName = eventLog.agencyOrElement,
                                AgentName = eventLog.agent,
                                Uri = eventLog.agentAvailable.Uri,
                                AgentRole = eventLog.agentAvailable.AgentRole,
                                TenantGroup = eventLog.agentAvailable.TenantGroup,
                                OperatorId = eventLog.agentAvailable.OperatorId,
                                Workstation = eventLog.agentAvailable.Workstation,
                                CloudId = Guid.NewGuid()
                            };

                            newSessionRecord.AgentAvailableRecords.Add(new AgentAvailableRecord
                            {
                                AgentSessionRecordId = newSessionRecord.Id,
                                StartAvailableInterval = eventLog.timestamp
                            });

                            
                                AgentAudit _newAgentaudit = new AgentAudit() {
                                    MediaLabel = eventLog.agentAvailable.MediaLabel,
                                    Logintime = eventLog.timestamp, //Do we consider this as a login time?
                                    TimeStamp = newSessionRecord.TimeStamp,
                                    isOnDuty = 1,
                                    isAvailable = 1,
                                    PsapName = eventLog.agencyOrElement,
                                    AgentName = eventLog.agent,
                                    Uri = eventLog.agentAvailable.Uri,
                                    AgentRole = eventLog.agentAvailable.AgentRole,
                                    TenantGroup = eventLog.agentAvailable.TenantGroup,
                                    OperatorId = eventLog.agentAvailable.OperatorId,
                                    Workstation = eventLog.agentAvailable.Workstation,
                                    Id = newSessionRecord.CloudId
                                };
                            newSessionRecord.AddAgentAudit(_newAgentaudit);
                            await _unitOfWork.UpdateAgentSession(newSessionRecord, userKeyData.ClientCode);
                            }

                        break;
                    }

                    case "AgentBusiedOut":
                    {
                        if (string.IsNullOrEmpty(eventLog.busiedOut?.MediaLabel) ||
                            eventLog.busiedOut.MediaLabel.Equals("."))
                                break;
                        var existingAgentSessionRecord = await FindAgentSessionRecord(eventLog.busiedOut.MediaLabel, userKeyData);

                            if (existingAgentSessionRecord != null)
                        {
                            var existingAgentBusiedRecord = existingAgentSessionRecord.AgentBusiedRecords.ToList();
                            var existingAgentAvailableRecord = existingAgentSessionRecord.AgentAvailableRecords.ToList();
                            //Build the busied out and available list entries for ELK and also update the tables agentbusiedrecord and agentavailable record in Maria DB if required
                            float totalBusiedMinutes = 0, totalAvailableMinutes = 0;
                            List<AvailableIntervalLog> listAgentAvailable = new List<AvailableIntervalLog>();
                            List<BusiedoutIntervalLog> listAgentBusiedOut = new List<BusiedoutIntervalLog>();
                            bool isnewAgentBusiedRecord = true;
                            foreach (var existingBusiedRecord in existingAgentBusiedRecord)
                            {
                                if (existingBusiedRecord.StartBusyInterval != null &&
                                    existingBusiedRecord.EndBusyInterval != null)
                                {
                                    if (existingBusiedRecord.IntervalTimeInMinutes == null)
                                    {
                                        existingBusiedRecord.IntervalTimeInMinutes = (float) existingBusiedRecord
                                            .EndBusyInterval.GetValueOrDefault()
                                            .Subtract(existingBusiedRecord.StartBusyInterval.GetValueOrDefault())
                                            .TotalMinutes;
                                    }

                                    listAgentBusiedOut.Add(new BusiedoutIntervalLog
                                    {
                                        StartBusyInterval = existingBusiedRecord.StartBusyInterval,
                                        EndBusyInterval = existingBusiedRecord.EndBusyInterval,
                                        IntervalTimeInMinutes = existingBusiedRecord.IntervalTimeInMinutes,
                                        BusiedOutAction = existingBusiedRecord.BusiedOutAction
                                    });
                                    totalBusiedMinutes +=
                                        existingBusiedRecord.IntervalTimeInMinutes.GetValueOrDefault();

                                }
                                else if (existingBusiedRecord.StartBusyInterval != null &&
                                         existingBusiedRecord.EndBusyInterval == null)
                                {
                                    isnewAgentBusiedRecord = false;
                                }

                            }

                            if (!existingAgentBusiedRecord.Any() || isnewAgentBusiedRecord)
                            {
                                existingAgentSessionRecord.AgentBusiedRecords.Add(new AgentBusiedRecord
                                {
                                    StartBusyInterval = eventLog.timestamp,
                                    BusiedOutAction = eventLog.busiedOut.BusiedOutAction
                                });
                            }

                            foreach (var existingAvailableRecord in existingAgentAvailableRecord)
                            {
                                if (existingAvailableRecord.StartAvailableInterval != null &&
                                    existingAvailableRecord.EndAvailableInterval != null)
                                {
                                    if (existingAvailableRecord.IntervalTimeInMinutes == null)
                                    {
                                        existingAvailableRecord.IntervalTimeInMinutes = (float) existingAvailableRecord
                                            .EndAvailableInterval.GetValueOrDefault()
                                            .Subtract(
                                                existingAvailableRecord.StartAvailableInterval.GetValueOrDefault())
                                            .TotalMinutes;
                                    }

                                    listAgentAvailable.Add(new AvailableIntervalLog
                                    {
                                        StartAvailableInterval = existingAvailableRecord.StartAvailableInterval,
                                        EndAvailableInterval = existingAvailableRecord.EndAvailableInterval,
                                        IntervalTimeInMinutes = existingAvailableRecord.IntervalTimeInMinutes
                                    });
                                    totalAvailableMinutes +=
                                        existingAvailableRecord.IntervalTimeInMinutes.GetValueOrDefault();
                                }
                                else if (existingAvailableRecord.StartAvailableInterval != null &&
                                         existingAvailableRecord.EndAvailableInterval == null)
                                {
                                    existingAvailableRecord.EndAvailableInterval =
                                        eventLog.timestamp;
                                    existingAvailableRecord.IntervalTimeInMinutes = (float) existingAvailableRecord
                                        .EndAvailableInterval.GetValueOrDefault()
                                        .Subtract(existingAvailableRecord.StartAvailableInterval.GetValueOrDefault())
                                        .TotalMinutes;
                                    
                                    listAgentAvailable.Add(new AvailableIntervalLog
                                    {
                                        StartAvailableInterval = existingAvailableRecord.StartAvailableInterval,
                                        EndAvailableInterval = existingAvailableRecord.EndAvailableInterval,
                                        IntervalTimeInMinutes = existingAvailableRecord.IntervalTimeInMinutes
                                    });
                                    totalAvailableMinutes +=
                                        existingAvailableRecord.IntervalTimeInMinutes.GetValueOrDefault();
                                }
                            }

                            //Update the Maria DB record for agent session-update the availableminutes and isAvailable flags,available minutes
                            existingAgentSessionRecord.isAvailable = 0;
                            existingAgentSessionRecord.availableTimeMinutes = totalAvailableMinutes;
                            existingAgentSessionRecord.busiedOutTimeMinutes = totalBusiedMinutes;

                            var elkExistingAuditRecords = existingAgentSessionRecord.FindAgentAudit(existingAgentSessionRecord.CloudId);
                            if (elkExistingAuditRecords == null)
                            {
                                AgentAudit _newAgentAudit = new AgentAudit() {
                                    AvailableIntervalLogs = listAgentAvailable,
                                    BusiedoutIntervalLogs = listAgentBusiedOut,
                                    MediaLabel = existingAgentSessionRecord.MediaLabel,
                                    Logintime = existingAgentSessionRecord.Logintime,
                                    TimeStamp = existingAgentSessionRecord.TimeStamp,
                                    isOnDuty = existingAgentSessionRecord.isOnDuty,
                                    isAvailable = existingAgentSessionRecord.isAvailable,
                                    PsapName = existingAgentSessionRecord.PsapName,
                                    AgentName = existingAgentSessionRecord.AgentName,
                                    Uri = existingAgentSessionRecord.Uri,
                                    AgentRole = existingAgentSessionRecord.AgentRole,
                                    TenantGroup = existingAgentSessionRecord.TenantGroup,
                                    OperatorId = existingAgentSessionRecord.OperatorId,
                                    Workstation = existingAgentSessionRecord.Workstation,
                                    Devicename = existingAgentSessionRecord.Devicename,
                                    Reason = existingAgentSessionRecord.Reason,
                                    Id = existingAgentSessionRecord.CloudId
                                };
                                existingAgentSessionRecord.AddAgentAudit(_newAgentAudit);
                                await _unitOfWork.UpdateAgentSession(existingAgentSessionRecord, userKeyData.ClientCode);
                                }
                            else
                            {
                                //Update all the required attributes in the agent session ELK record
                                elkExistingAuditRecords.BusiedoutIntervalLogs = listAgentBusiedOut;
                                elkExistingAuditRecords.AvailableIntervalLogs = listAgentAvailable;
                                elkExistingAuditRecords.isAvailable = 0;
                                elkExistingAuditRecords.availableTimeMinutes = totalAvailableMinutes;
                                elkExistingAuditRecords.busiedOutTimeMinutes = totalBusiedMinutes;
                                existingAgentSessionRecord.UpdateAgentAudit(elkExistingAuditRecords);
                                await _unitOfWork.UpdateAgentSession(existingAgentSessionRecord, userKeyData.ClientCode);
                                }
                        }
                            else
                        {
                            AgentSessionRecord newSessionRecord = new AgentSessionRecord
                            {
                                MediaLabel = eventLog.busiedOut.MediaLabel,
                                TimeStamp = eventLog.timestamp,
                                isOnDuty =
                                    0, //TBD   What should be the value of the new entry if BusiedOut is received?
                                isAvailable = 0,
                                PsapName = eventLog.agencyOrElement,
                                AgentName = eventLog.agent,
                                Uri = eventLog.busiedOut.Uri,
                                AgentRole = eventLog.busiedOut.AgentRole,
                                TenantGroup = eventLog.busiedOut.TenantGroup,
                                OperatorId = eventLog.busiedOut.OperatorId,
                                Workstation = eventLog.busiedOut.Workstation,
                                CloudId = Guid.NewGuid(),
                            };
                            newSessionRecord.AgentBusiedRecords.Add(new AgentBusiedRecord
                            {
                                StartBusyInterval = eventLog.timestamp,
                                BusiedOutAction = eventLog.busiedOut.BusiedOutAction
                            });

                            
                            AgentAudit _newAgentAudit = new AgentAudit() 
                            {
                                MediaLabel = eventLog.busiedOut.MediaLabel,
                                TimeStamp = newSessionRecord.TimeStamp,
                                isOnDuty =
                                0, //TBD   What should be the value of the new entry if BusiedOut is received?
                                isAvailable = 0,
                                PsapName = eventLog.agencyOrElement,
                                AgentName = eventLog.agent,
                                Uri = eventLog.busiedOut.Uri,
                                AgentRole = eventLog.busiedOut.AgentRole,
                                TenantGroup = eventLog.busiedOut.TenantGroup,
                                OperatorId = eventLog.busiedOut.OperatorId,
                                Workstation = eventLog.busiedOut.Workstation,
                                Id = newSessionRecord.CloudId
                            };
                            newSessionRecord.AddAgentAudit(_newAgentAudit);
                            await _unitOfWork.UpdateAgentSession(newSessionRecord, userKeyData.ClientCode);
                        }

                        break;
                    }
                    //HELDresponse - non-serialized processing of field.
                    case "HELDresponse":
                    {
                        EventDataHandler.LocationData locationHandler = new EventDataHandler.LocationData();
                        locationHandler.PopulateEventLogByHELD(eventLog, eventDataXml);
                        
                        break;
                    }
                    //EIDD - secondary source of Location information, used to secondary logic checks as HELDresponse should dictate 
                    case "EIDD":
                    {
                        EventDataHandler.LocationData locationHandler = new EventDataHandler.LocationData();
                        locationHandler.PopulateEventLogByEIDD(eventLog, eventDataXml);

                        break;
                    }
                }

                int addedEventId = await _unitOfWork.AddEvent(userKeyData.ClientCode, eventLog);
                //Add the prepared event before doing the data pull check on processing.
                if (addedEventId > 0)
                {
                    eventAdded = true;
                }

                //NOTE: this condition is important, to avoid false positives unless the expected events are being presented.
                if (eventLog.endCall != null || eventLog.endMedia != null || (userKeyData.ClientEsiNETSupported && eventLog.cdrType1 != null))
                {
         
                    (Dictionary<string, int> eventCountLookup, int maxStateId, int maxEventId) = await _unitOfWork.GetEventTypeCount(eventLog.callIdentifier, userKeyData.ClientCode, addedEventId);

                    
                    if( maxStateId > 1) //condition, events previously processed (or other state beyond new) - set to process queue to delay processing - to avoid thrashing of event processing
                    {
                        //Set to ProcessQueue
                        await _unitOfWork.SetProcessQueue(eventLog.callIdentifier, userKeyData.ClientCode);
                        _logger.LogInformation($"Set Call to the Process Queue {eventLog.callIdentifier}, {userKeyData.ClientCode} - maxStateId: {maxStateId}");
                    }
                    else if( DetermineIfCallIsFinished(eventCountLookup, userKeyData.ClientEsiNETSupported, eventLog.callIdentifier))
                    {
                        List<EventLog> eventLogList = await _unitOfWork.GetEventsForProcessing(eventLog.callIdentifier, userKeyData.ClientCode, maxEventId);

                        RootEvent existingEventRoot=new RootEvent { CallId = eventLog.callIdentifier };
                        foreach (EventLog eventlog in eventLogList)
                        {
                            existingEventRoot.Events.Add(eventlog);
                        }

                        await ProcessCall(existingEventRoot, userKeyData.ClientCode, userKeyData.TenantLookup, userKeyData.ClientTimezone, maxEventId);
                    }
                }
                eventHash.Islastsavesuccessful = true;

                response.IsSuccess = true;
                return response;
            }

            catch (Exception e)
            {
                var exceptionMessage = e.FormatException();
                eventHash.Islastsavesuccessful = false;

                /// Can cause issues with the finally clause writting to the DB - where it will be reingested with corrupeted XML structure.
                /// We can see if the need to store the exception details to the Events is required in future and refactor the behaviour.
                ///newEvent.Message = exceptionMessage;

                await _unitOfWork.LogError(userKeyData.ClientCode, eventLog.callIdentifier);

                if (eventLog!=null && !string.IsNullOrEmpty(eventLog.callIdentifier))
                    throw new Exception("Could not save event: " + exceptionMessage +
                                        "\n The exception occured in Event Type: " + eventLog.eventType +
                                        " for the CallID:" + eventLog.callIdentifier);
                else
                    throw new Exception("Could not save event: " + exceptionMessage +
                                        "\n The exception occured in Agent Audit Event Type: " + eventLog.eventType);
            }
            finally
            {
                //Add the event to the process table if it has not been added yet - catches case of process failing to make sure no matter what, the Event data is tracked. (as long as it isn't a repeat event/already processed)
                if(!repeatEvent && !eventAdded && !ignoreEvent)
                {
                    await _unitOfWork.AddEvent(userKeyData.ClientCode, eventLog);
                }
                
                if(!repeatEvent)    //no need to add it to the hash if already added/exists.
                {

                    await _unitOfWork.UpdateHashEvent(eventHash);
                }
            }
        }

        /// <summary>
        /// Determines if the call is finnished.  Based on key event counts and CDR configuration/event
        /// </summary>
        /// <param name="existingEventRoot">Collection of existing events</param>
        /// <param name="esiNETSupported">Flags to check for the CDR event for EsiNet clients</param>
        /// <returns>if call is ready for processing or not</returns>
        private bool DetermineIfCallIsFinished(Dictionary<string, int> eventCountLookup, bool esiNETSupported, string callIdentifier)
        {
            if( eventCountLookup == null || eventCountLookup.Count == 0)
            {
                return false;
            }
            int startCallCount = eventCountLookup.GetValueOrDefault("startcall", 0);
            int outboundCallCount = eventCountLookup.GetValueOrDefault("outboundcall", 0); 
            int endCallCount = eventCountLookup.GetValueOrDefault("endcall", 0); 
            int mediaCount = eventCountLookup.GetValueOrDefault("media", 0);
            int endMediaCount = eventCountLookup.GetValueOrDefault("endmedia", 0);
            int cdrType1Count = eventCountLookup.GetValueOrDefault("cdrtype1", 0);

            //detected condition when there is no start/outbound/endcall event, it can lead to a false processing start.
            if(startCallCount == 0 && outboundCallCount == 0 && endCallCount == 0)
            {
                return false;
            }

            //confirm the call is considered data complete based on the Events being closed off
            bool callEventsMatch = ((startCallCount + outboundCallCount) == endCallCount) && (mediaCount == endMediaCount);
            bool callReadyForProcessing = false;

            //Jump into the additional CDR check logic only if configured/required
            if (esiNETSupported)
            {
                //confirm the CDRType1 event has been received
                bool cdrTypeDefined = (esiNETSupported && cdrType1Count > 0);

                if (endCallCount == 0 && endMediaCount == 0)
                {
                    _logger.LogInformation($"DetermineIfCallIsFinnished: No EndCall/EndMedia for {callIdentifier}");
                    return false;
                }

                // Outbound does not require CDRType1 to be present
                if (outboundCallCount != 0)
                {
                    callReadyForProcessing = callEventsMatch;
                }
                else
                {
                    callReadyForProcessing = callEventsMatch && cdrTypeDefined;
                }
            }
            else
            {
                callReadyForProcessing = callEventsMatch;
            }

            return callReadyForProcessing;
        }

        /// <summary>
        /// Processes the call based on passed events.
        /// </summary>
        /// <param name="existingEventRoot">Collection of Events</param>
        /// <param name="clientCode">Unique Client idenfiier</param>
        /// <param name="tenantLookup">Lookup for multi-tenant systems</param>
        /// <param name="clientTimezone">Specific customers Timezone to shift key timestamps by</param>
        /// <param name="maxEventId">Maximum Event associated to this processing, -1 indiciates there is none, and all events are used.</param>
        /// <param name="expiredProcessingOccurrence">optional flag to indicate this is an expired processing occurence - to enable settings of Expired state on failure vs Error state</param>
        /// <returns>successful</returns>
        /// <remarks>self contained logging on failure.</remarks>
        public async Task<bool> ProcessCall(RootEvent existingEventRoot, string clientCode, Dictionary<string,string> tenantLookup, NodaTime.DateTimeZone clientTimezone, int maxEventId, bool expiredProcessingOccurrence = false)
        {
            var result = new List<CallSummary>();
            try 
            {
                //NOTE: Original flow was it was expected the Root Call Summary record existed at this point, new logic now delays the write of the Root call summary record
                //until during the ProcessCall action (introduced due to Tenant mapping logic for unique indexes) - therefore logic still attempts to retrieve the ES Root call, but 
                //expects NULL data, therefore the additional checks.
                //TODO: https://solacomtech.atlassian.net/browse/INFO-1210 - Review: ProcessCall initially fetching Call Summary record

                _logger.LogInformation($"Processing all events for the call {clientCode}:{existingEventRoot.CallId}");
                existingEventRoot.Events = existingEventRoot.Events.OrderBy(e => e.timestamp).ToList();
                
                List<CallSummary> rootCallSummary = null;
                
                //Create ES Root Call Summary
                var startOrOutboundCall = existingEventRoot.Events.Where(e => e.startCall != null || e.outboundCall != null).OrderBy(e => e.timestamp).ToList().FirstOrDefault();
                //edge condition if processing occurs on a none qualified call sequence with out require start/outbound event start point.  Controlled exception to handle the state condition.
                if( startOrOutboundCall == null)
                {
                    throw new Exception($"Start/Outbound events do not exist, cannot process call. {clientCode}:{existingEventRoot.CallId}");
                }
                //Retrieve the generated Root Call Summary record - NOTE: no longer writes this to the index at this point
                rootCallSummary = GenerateESRootCallSummary(startOrOutboundCall);
                result.Add(rootCallSummary.FirstOrDefault());
                existingEventRoot.IsESRootExists = true;
                
                result = PopulateAllAnswerRecords(existingEventRoot, result);  
                result = PopulateMessageFields(existingEventRoot, result);  
                result = PopulateALIFields(existingEventRoot, result, clientCode);      
                result = PopulateEndTime(existingEventRoot, result); 
                result = PopulateCallState(existingEventRoot, result);  
                result = PopulateCallbackFields(existingEventRoot, result);
                PopulateLocationData(existingEventRoot, result);
                PromoteESInetData(existingEventRoot, result);

                //routine to trim off the LocationDataList object from children nodes - required as the upper business logic does full node duplication
                for(int i = 1; i < result.Count; i++)
                {
                    result[i].LocationDataList = null;
                }


                //set the root event with the call compelte state, main use case it to capture when a incomplete event parsing record is generated.
                if ( result != null && result.Count > 0)
                {
                    #region Admin call ANI logic 

                    //Only perform the secondary ANI fetch when it is an admin call. 
                    //secondary check is in case new logic comes into play, this should ONLY set callbacknumber if there isn't one already set.
                    //Startcall.ANI is a non-validated number, the first number passed from the caller - for normal emergency calls, this number is updated based on rebids/ALI/HELD/ADR information.  For administration lines, it is a valid number sicne no further rebids occur.
                    if (result[0].IsAdmin && string.IsNullOrEmpty(result[0].CallBackNumber))
                    {
                        List<EventLog> startEvents = existingEventRoot.Events.Where(e => e.startCall != null).OrderBy(e => e.timestamp).ToList();
                        if (startEvents != null && startEvents.Count > 0)
                        {
                            string adminCallbacknumber = startEvents[0].startCall.Ani;
                            foreach(CallSummary callsummary in result)
                            {
                                callsummary.CallBackNumber = adminCallbacknumber;
                            }
                        }
                        else
                        {
                            _logger.LogWarning($"Start Call events do not exist, cannot process startcall.Ani {existingEventRoot.CallId}");
                        }

                    }
                    #endregion
                }

                //First, confirm if the records previously existed - to allow for cleaning before writting the updated process records.
                (bool callExists, DateTime dateCreated, long callSummaryCount) = await _unitOfWork_InsightsData.CallSummaryExists(clientCode, existingEventRoot.CallId);
                if( callExists)
                {
                    _logger.LogWarning($"Call: {clientCode}:{existingEventRoot.CallId} was previously processed on {dateCreated} with resulting {callSummaryCount} call Summary records.  Clearing and re-ingesting.");

                    (long callSummaryCountDeleted, long callEventCountDeleted) = await _unitOfWork_InsightsData.DeleteCallData(clientCode, existingEventRoot.CallId);

                    _logger.LogWarning($"Call: {clientCode}:{existingEventRoot.CallId} cleared {callSummaryCountDeleted} call Summary records and {callEventCountDeleted} call Event records.");
                }

                //Logic section to shift all populate ToLocal (timezone shifted) properities on the final data set.

                //For the Call summary data set.  Doing inline checks to not try to process null fields.
                foreach (CallSummary callSummary in result)
                {
                    if (callSummary.CallAnswered != null)
                    {
                        callSummary.CallAnsweredToLocal = TimezoneHelper.GetTimeAsLocal(callSummary.CallAnswered.Value, clientTimezone);
                    }
                    if (callSummary.CallArrivedSystem != null)
                    {
                        callSummary.CallArrivedSystemToLocal = TimezoneHelper.GetTimeAsLocal(callSummary.CallArrivedSystem.Value, clientTimezone);
                    }
                    if (callSummary.CallPresented != null)
                    {
                        callSummary.CallPresentedToLocal = TimezoneHelper.GetTimeAsLocal(callSummary.CallPresented.Value, clientTimezone);
                    }
                    if (callSummary.CallTransferred != null)
                    {
                        callSummary.CallTransferredToLocal = TimezoneHelper.GetTimeAsLocal(callSummary.CallTransferred.Value, clientTimezone);
                    }
                    if (callSummary.CallReleased != null)
                    {
                        callSummary.CallReleasedToLocal = TimezoneHelper.GetTimeAsLocal(callSummary.CallReleased.Value, clientTimezone);
                    }
                    if (callSummary.EndCallTime != null)
                    {
                        callSummary.EndCallTimeToLocal = TimezoneHelper.GetTimeAsLocal(callSummary.EndCallTime.Value, clientTimezone);
                    }
                    if (callSummary.StartCallTime != null)
                    {
                        callSummary.StartCallTimeToLocal = TimezoneHelper.GetTimeAsLocal(callSummary.StartCallTime.Value, clientTimezone);
                    }

                    callSummary.TimeStampToLocal = TimezoneHelper.GetTimeAsLocal(callSummary.TimeStamp, clientTimezone);
                }

                //For the Event data set.
                foreach (EventLog eventLog in existingEventRoot.Events)
                {
                    eventLog.timestampToLocal = TimezoneHelper.GetTimeAsLocal(eventLog.timestamp, clientTimezone);
                }


                //PopulateAliEventsInIndex(existingEventRoot);
                //Only configured Call Summary index write point.
                await InsertCallSummaries(result, clientCode, tenantLookup);
                
                //Add the Events data to the final DB
                await _unitOfWork_InsightsData.SetCallEventByList(clientCode, "", existingEventRoot.Events);
                
                //finally, set the processed state on the Events table.
                await _unitOfWork.SetEventProcessed(existingEventRoot.CallId, clientCode, maxEventId);

                return true;
            }
            catch(Exception ex)
            {
                _logger.LogError(ex, $"Processing of events failed for Call: {clientCode}:{existingEventRoot.CallId}");
                
                //Update all events for the given call to an error state.
                try
                {
                    
                    if( expiredProcessingOccurrence )
                    {
                        //if processing expired events, set the failure case to Expired state, to enable handling of logic later on attempted reprocessing events.
                        await _unitOfWork.SetExpiredEventState(existingEventRoot.CallId, clientCode, maxEventId);
                        _logger.LogWarning($"Set Events to Expired state for Call: {clientCode}:{existingEventRoot.CallId}");
                    }
                    else
                    {
                        await _unitOfWork.SetErrorEventState(existingEventRoot.CallId, clientCode, maxEventId);
                    }
                    
                }
                catch (Exception exDB)
                {
                    _logger.LogError(exDB, $"Failed to update the Error state of the Event for call {clientCode}:{existingEventRoot.CallId}");
                }

                return false;
            }
        }

        /// <summary>
        /// Populates the Location Data collection to the Root record
        /// </summary>
        /// <param name="existingRootEvent">Existing root event object</param>
        /// <param name="result">Resulting Call summary collection - updated in process</param>
        /// <exception cref="Exception"></exception>
        private void PopulateLocationData(RootEvent existingRootEvent, List<CallSummary> result)
        {
            var locationDataResults = existingRootEvent.Events.Where(e => e.heldResponse != null && e.heldResponse.LocationData != null).OrderBy(e => e.timestamp).ToList();

            ElasticSearch.Entities.LocationData locationDataEntity;
            foreach(var locationDataEvent in locationDataResults)
            {
                locationDataEntity = new ElasticSearch.Entities.LocationData(locationDataEvent.heldResponse.LocationData.LocationEventSource);
                
                locationDataEntity.TimeStamp = locationDataEvent.timestamp;

                if (locationDataEvent.heldResponse.LocationData.Confidence != null)
                {
                    locationDataEntity.Confidence = new ElasticSearch.Entities.Confidence();

                    locationDataEntity.Confidence.Pdf = locationDataEvent.heldResponse.LocationData.Confidence.Pdf;
                    locationDataEntity.Confidence.Text = locationDataEvent.heldResponse.LocationData.Confidence.Text;
                }

                if( locationDataEvent.heldResponse.LocationData.Method != null)
                {
                    locationDataEntity.Method = locationDataEvent.heldResponse.LocationData.Method;
                }

                //population of the ServiceInfo and related COS calculation
                if( locationDataEvent.heldResponse.LocationData.ServiceInfo != null)
                {
                    locationDataEntity.ServiceInfo = new ElasticSearch.Entities.ServiceInfo();
                    locationDataEntity.ServiceInfo.ServiceEnvironment = locationDataEvent.heldResponse.LocationData.ServiceInfo.ServiceEnvironment;
                    locationDataEntity.ServiceInfo.ServiceMobility = locationDataEvent.heldResponse.LocationData.ServiceInfo.ServiceMobility;
                    locationDataEntity.ServiceInfo.ServiceType = locationDataEvent.heldResponse.LocationData.ServiceInfo.ServiceType;
                    locationDataEntity.ServiceInfo.Legacy_Class_Of_service = locationDataEvent.heldResponse.LocationData.ServiceInfo.Legacy_Class_Of_service;

                    //Calculate the COS based on expected data.
                    string serviceCOS  = Entities.ClassOfService.GetCOSValue(  locationDataEntity.Method,
                                                                               locationDataEntity.ServiceInfo.ServiceType,
                                                                               locationDataEntity.ServiceInfo.ServiceEnvironment,
                                                                               locationDataEntity.ServiceInfo.ServiceMobility                                                                                
                                                                            );

                    //Logging of unexpected case of empty COS when information was presented.
                    if( string.IsNullOrEmpty(serviceCOS) && 
                        !(string.IsNullOrEmpty(locationDataEntity.ServiceInfo.ServiceType) && string.IsNullOrEmpty(locationDataEntity.ServiceInfo.ServiceMobility)))
                    {
                        _logger.LogWarning($"PIDFLO and ADR did not locate a matching ClassOfService.  For {result[0].CallIdentifier}, method:{locationDataEntity.Method} - {locationDataEntity.ServiceInfo.ToString()}");
                    }
                    else if (!string.IsNullOrEmpty(serviceCOS))  //set the COS if the value isn't null/empty - Logic introduced when the Service info is in later event from the comment COS provided
                    {
                        locationDataEntity.COS = serviceCOS;
                    }
                }

                if (locationDataEvent.heldResponse.LocationData.DeviceInfo != null)
                {
                    locationDataEntity.DeviceInfo = new ElasticSearch.Entities.DeviceInfo();
                    locationDataEntity.DeviceInfo.DataProviderReference = locationDataEvent.heldResponse.LocationData.DeviceInfo.DataProviderReference;
                    locationDataEntity.DeviceInfo.DeviceClassification = locationDataEvent.heldResponse.LocationData.DeviceInfo.DeviceClassification;
                    locationDataEntity.DeviceInfo.DeviceMfgr = locationDataEvent.heldResponse.LocationData.DeviceInfo.DeviceMfgr;
                    locationDataEntity.DeviceInfo.DeviceModelNr = locationDataEvent.heldResponse.LocationData.DeviceInfo.DeviceModelNr;
                    locationDataEntity.DeviceInfo.TypeOfDeviceID = locationDataEvent.heldResponse.LocationData.DeviceInfo.TypeOfDeviceID;
                    locationDataEntity.DeviceInfo.UniqueDeviceID = locationDataEvent.heldResponse.LocationData.DeviceInfo.UniqueDeviceID;
                }

                if (locationDataEvent.heldResponse.LocationData.ProviderInfo != null)
                {
                    locationDataEntity.ProviderInfo = new ElasticSearch.Entities.ProviderInfo();
                    locationDataEntity.ProviderInfo.DataProviderReference = locationDataEvent.heldResponse.LocationData.ProviderInfo.DataProviderReference;
                    locationDataEntity.ProviderInfo.DataProviderString = locationDataEvent.heldResponse.LocationData.ProviderInfo.DataProviderString;
                    locationDataEntity.ProviderInfo.ProviderID = locationDataEvent.heldResponse.LocationData.ProviderInfo.ProviderID;
                    locationDataEntity.ProviderInfo.ProviderIDSeries = locationDataEvent.heldResponse.LocationData.ProviderInfo.ProviderIDSeries;
                    locationDataEntity.ProviderInfo.TypeOfProvider = locationDataEvent.heldResponse.LocationData.ProviderInfo.TypeOfProvider;
                    locationDataEntity.ProviderInfo.ContactURI = locationDataEvent.heldResponse.LocationData.ProviderInfo.ContactURI;
                    locationDataEntity.ProviderInfo.Language = locationDataEvent.heldResponse.LocationData.ProviderInfo.Language;

                    //setting the COS based on the Comment field for specific provider cases.
                    //Cross checking that the matched comment is valid and that if the previous calculated COS came back as Unkown - to thus reduce the potential re-fetching of COS when a valid entry is found.
                    if (locationDataEvent.heldResponse.LocationData.EmergencyCallDataComment != null &&
                        !string.IsNullOrEmpty(locationDataEvent.heldResponse.LocationData.EmergencyCallDataComment.Comment)
                        )
                    {
                        string[] parsedComment = locationDataEvent.heldResponse.LocationData.EmergencyCallDataComment.Comment.Split("-");
                        if (parsedComment.Length > 0)
                        {
                            string originalCOS = locationDataEntity.COS;
                            locationDataEntity.COS = Entities.ClassOfService.ValidateCOSValue(parsedComment[0].Trim()); //Sub function which matches the comment to know COS, if not found, sets it back to Unknown
                            if (locationDataEntity.COS == "Unknown")  //Check to see if the COS came back unknown still
                            {
                                _logger.LogWarning($"COS determined from EmergencyCallData.Comment did not resolve. For {result[0].CallIdentifier}, comment field was : {parsedComment[0].Trim()}.");
                            }
                            else
                            {
                                _logger.LogInformation($"COS determined from EmergencyCallData.Comment. For {result[0].CallIdentifier} set to: {locationDataEntity.COS}");
                            }

                        }

                    }
                }

                if (locationDataEvent.heldResponse.LocationData.CivicAddress != null)
                {
                    locationDataEntity.CivicAddress = new ElasticSearch.Entities.CivicAddress();

                    locationDataEntity.CivicAddress = new ElasticSearch.Entities.CivicAddress();
                    locationDataEntity.CivicAddress.Country = locationDataEvent.heldResponse.LocationData.CivicAddress.Country;
                    locationDataEntity.CivicAddress.A1 = locationDataEvent.heldResponse.LocationData.CivicAddress.A1;
                    locationDataEntity.CivicAddress.A2 = locationDataEvent.heldResponse.LocationData.CivicAddress.A2;
                    locationDataEntity.CivicAddress.A3 = locationDataEvent.heldResponse.LocationData.CivicAddress.A3;
                    locationDataEntity.CivicAddress.A4 = locationDataEvent.heldResponse.LocationData.CivicAddress.A4;
                    locationDataEntity.CivicAddress.A5 = locationDataEvent.heldResponse.LocationData.CivicAddress.A5;
                    locationDataEntity.CivicAddress.A6 = locationDataEvent.heldResponse.LocationData.CivicAddress.A6;
                    locationDataEntity.CivicAddress.PRM = locationDataEvent.heldResponse.LocationData.CivicAddress.PRM;
                    locationDataEntity.CivicAddress.PRD = locationDataEvent.heldResponse.LocationData.CivicAddress.PRD;
                    locationDataEntity.CivicAddress.RD = locationDataEvent.heldResponse.LocationData.CivicAddress.RD;
                    locationDataEntity.CivicAddress.STS = locationDataEvent.heldResponse.LocationData.CivicAddress.STS;
                    locationDataEntity.CivicAddress.POD = locationDataEvent.heldResponse.LocationData.CivicAddress.POD;
                    locationDataEntity.CivicAddress.POM = locationDataEvent.heldResponse.LocationData.CivicAddress.POM;
                    locationDataEntity.CivicAddress.RDSEC = locationDataEvent.heldResponse.LocationData.CivicAddress.RDSEC;
                    locationDataEntity.CivicAddress.RDSUBBR = locationDataEvent.heldResponse.LocationData.CivicAddress.RDSUBBR;
                    locationDataEntity.CivicAddress.HNO = locationDataEvent.heldResponse.LocationData.CivicAddress.HNO;
                    locationDataEntity.CivicAddress.HNS = locationDataEvent.heldResponse.LocationData.CivicAddress.HNS;
                    locationDataEntity.CivicAddress.LMK = locationDataEvent.heldResponse.LocationData.CivicAddress.LMK;
                    locationDataEntity.CivicAddress.LOC = locationDataEvent.heldResponse.LocationData.CivicAddress.LOC;
                    locationDataEntity.CivicAddress.FLR = locationDataEvent.heldResponse.LocationData.CivicAddress.FLR;
                    locationDataEntity.CivicAddress.NAM = locationDataEvent.heldResponse.LocationData.CivicAddress.NAM;
                    locationDataEntity.CivicAddress.PC = locationDataEvent.heldResponse.LocationData.CivicAddress.PC;
                    locationDataEntity.CivicAddress.BLD = locationDataEvent.heldResponse.LocationData.CivicAddress.BLD;
                    locationDataEntity.CivicAddress.UNIT = locationDataEvent.heldResponse.LocationData.CivicAddress.UNIT;
                    locationDataEntity.CivicAddress.ROOM = locationDataEvent.heldResponse.LocationData.CivicAddress.ROOM;
                    locationDataEntity.CivicAddress.SEAT = locationDataEvent.heldResponse.LocationData.CivicAddress.SEAT;
                    locationDataEntity.CivicAddress.PLC = locationDataEvent.heldResponse.LocationData.CivicAddress.PLC;
                    locationDataEntity.CivicAddress.PCN = locationDataEvent.heldResponse.LocationData.CivicAddress.PCN;
                    locationDataEntity.CivicAddress.POBOX = locationDataEvent.heldResponse.LocationData.CivicAddress.POBOX;
                    locationDataEntity.CivicAddress.ADDCODE = locationDataEvent.heldResponse.LocationData.CivicAddress.ADDCODE;
                }

                if (locationDataEvent.heldResponse.LocationData.Ellipse != null)
                {
                    locationDataEntity.Ellipse = new ElasticSearch.Entities.Ellipse();

                    locationDataEntity.Ellipse.Point = ElasticSearch.Entities.Point.ParsePointString(locationDataEvent.heldResponse.LocationData.Ellipse.POS);
                    locationDataEntity.Ellipse.SemiMajorAxis = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Ellipse.SemiMajorAxis);
                    locationDataEntity.Ellipse.SemiMinorAxis = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Ellipse.SemiMinorAxis);
                    locationDataEntity.Ellipse.Orientation = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Ellipse.Orientation);
                }

                if (locationDataEvent.heldResponse.LocationData.Ellipsoid != null)
                {
                    locationDataEntity.Ellipsoid = new ElasticSearch.Entities.Ellipsoid();

                    locationDataEntity.Ellipsoid.Point = ElasticSearch.Entities.Point.ParsePointString(locationDataEvent.heldResponse.LocationData.Ellipsoid.POS);
                    locationDataEntity.Ellipsoid.SemiMajorAxis = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Ellipsoid.SemiMajorAxis);
                    locationDataEntity.Ellipsoid.SemiMinorAxis = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Ellipsoid.SemiMinorAxis);
                    locationDataEntity.Ellipsoid.Orientation = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Ellipsoid.Orientation);
                    locationDataEntity.Ellipsoid.VerticalAxis = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Ellipsoid.VerticalAxis);
                }

                if (locationDataEvent.heldResponse.LocationData.ArcBand != null)
                {
                    locationDataEntity.ArcBand = new ElasticSearch.Entities.ArcBand();
                    
                    locationDataEntity.ArcBand.Point = ElasticSearch.Entities.Point.ParsePointString(locationDataEvent.heldResponse.LocationData.ArcBand.POS);
                    locationDataEntity.ArcBand.InnerRadius = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.ArcBand.InnerRadius);
                    locationDataEntity.ArcBand.OuterRadius = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.ArcBand.OuterRadius);
                    locationDataEntity.ArcBand.StartAngle = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.ArcBand.StartAngle);
                    locationDataEntity.ArcBand.OpeningAngle = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.ArcBand.OpeningAngle);
                }

                if (locationDataEvent.heldResponse.LocationData.Circle != null)
                {
                    locationDataEntity.Circle = new ElasticSearch.Entities.Circle();

                    locationDataEntity.Circle.Point = ElasticSearch.Entities.Point.ParsePointString(locationDataEvent.heldResponse.LocationData.Circle.POS);
                    locationDataEntity.Circle.Radius = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Circle.Radius);
                }
                if (locationDataEvent.heldResponse.LocationData.Sphere != null)
                {
                    locationDataEntity.Sphere = new ElasticSearch.Entities.Sphere();
                    locationDataEntity.Sphere.Point = ElasticSearch.Entities.Point.ParsePointString(locationDataEvent.heldResponse.LocationData.Sphere.POS);
                    locationDataEntity.Sphere.Radius = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Sphere.Radius);
                }

                if (locationDataEvent.heldResponse.LocationData.Point != null)
                {
                    locationDataEntity.Point = new ElasticSearch.Entities.Point();

                    locationDataEntity.Point = ElasticSearch.Entities.Point.ParsePointString(locationDataEvent.heldResponse.LocationData.Point.POS);
                }

                if (locationDataEvent.heldResponse.LocationData.Polygon != null)
                {
                    locationDataEntity.Polygon = new ElasticSearch.Entities.Polygon();

                    locationDataEntity.Polygon.Point = ElasticSearch.Entities.Point.ParsePointString(locationDataEvent.heldResponse.LocationData.Polygon.POS);
                    locationDataEntity.Polygon.PointList = ElasticSearch.Entities.Polygon.ProcessStringToPolygon(locationDataEvent.heldResponse.LocationData.Polygon.ExteriorPointCollection);
                }
                if (locationDataEvent.heldResponse.LocationData.Prism != null)
                {
                    locationDataEntity.Prism = new ElasticSearch.Entities.Prism();

                    locationDataEntity.Prism.Height = XmlHelper.ConvertToDouble(locationDataEvent.heldResponse.LocationData.Prism.Height);
                    locationDataEntity.Prism.PointList = ElasticSearch.Entities.Polygon.ProcessStringToPolygon(locationDataEvent.heldResponse.LocationData.Prism.ExteriorPointCollection);
                }

                //Adding the collection to the Root Call Summary record only.
                result[0].LocationDataList.Add(locationDataEntity);
            }

            //No need to return result, auto sent by reference.
            
        }

        /// <summary>
        /// Promotes data captured from ESInet sources to key records based if they have previously been set or not.  
        /// </summary>
        /// <param name="existingRootEvent">Object storing the events</param>
        /// <param name="result">Collection of Call Summary records</param>
        /// <remarks>This logic could be optimized to be part of PopulateLocationData, however, keeping it seperate to enable clean disconnect from the source of the data and processing.</remarks>
        private void PromoteESInetData(RootEvent existingRootEvent, List<CallSummary> result)
        {           
            //confirm if there is location data / ESInet available
            if( result[0].LocationDataList == null || result[0].LocationDataList.Count == 0)
            {
                return;
            }

            //to track the updated fields, to retuce the number of re-interation updates once Call Summary Root is updated.
            List<string> trackingCollection = new List<string>();

            //used to track call mobility type enum, to save having to reset on later usage.
            CallMobilityType callmobilityType = CallMobilityType.Unknown;

            //Callbacknumber - Completed first as it is idependent of the LocationData/EsiNet data being present. 
            if ( string.IsNullOrEmpty(result[0].CallBackNumber))
            {
                //Event is passed through to only extract from CDR type for this specific field.
                List<EventLog> cdrEvents = existingRootEvent.Events.Where(e => e.cdrType1 != null).OrderBy(e => e.timestamp).ToList();
                if( cdrEvents != null && cdrEvents.Count > 0)
                {
                    //warning logged, as expected spec is a single CDR event per call 
                    if (cdrEvents.Count > 1)
                    {
                        _logger.LogWarning($"Detected multiple CDRType Events, {cdrEvents.Count} cdr events for {existingRootEvent.CallId}");
                    }

                    EventLog firstCDREvent = cdrEvents[0];

                    if (string.IsNullOrEmpty(firstCDREvent.cdrType1.CallbackNumber))
                    {
                        _logger.LogError($"First CDR event contains empty Callbacknumber {existingRootEvent.CallId}");
                        //Note: future logic could introduce expanded seeking of callbacknumber against a list of CDR events
                        // i.e. firstCDREvent = cdrEvents.Where(e => !string.IsNullOrEmpty(e.cdrType1.CallbackNumber)).FirstOrDefault();
                    }
                    else
                    {
                        result[0].CallBackNumber = firstCDREvent.cdrType1.CallbackNumber;
                        trackingCollection.Add("CallbackNumber");
                    }
                }
            }

            //pull the Location data collection from the Root Summary object
            //based on data, if not set, populate.  ALI parsing is before this function, so only UPDATE the field if empty / not set from ALI.
            //confirm if there is location data / ESInet available
            if (result[0].LocationDataList != null || result[0].LocationDataList.Count > 0)
            {
                //COS
                if (string.IsNullOrEmpty(result[0].FinalCos))
                {
                    var finalCOS_Promote = result[0].LocationDataList.Where(e => !string.IsNullOrEmpty(e.COS)).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                    if( finalCOS_Promote != null)
                    {
                        result[0].FinalCos = finalCOS_Promote.COS;
                        trackingCollection.Add("FinalCos");
                    }

                    //warpped original COS into the final COS condition - if there was a final cos, than we shouldn't set the original COS from ADR
                    if (string.IsNullOrEmpty(result[0].OriginalCos))
                    {
                        var originalCOS_Promote = result[0].LocationDataList.Where(e => !string.IsNullOrEmpty(e.COS)).OrderBy(e => e.TimeStamp).FirstOrDefault();
                        if (originalCOS_Promote != null)
                        {
                            result[0].OriginalCos = originalCOS_Promote.COS;
                            trackingCollection.Add("OriginalCos");
                        }
                    }
                }

                //address
                if( string.IsNullOrEmpty(result[0].Address))
                {
                    var civicAddress_promote = result[0].LocationDataList.Where(e => e.CivicAddress != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();

                    if( civicAddress_promote != null)
                    {
                        //format is streetNumber + Address - save speed, trim is used to remove the space IF there is no streetnumber available (instead of conditional check if space should be presented). 
                        result[0].Address = $"{civicAddress_promote.CivicAddress.HNO} {civicAddress_promote.CivicAddress.RD} {civicAddress_promote.CivicAddress.STS}".Trim();
                        trackingCollection.Add("Address");
                    }
                }

                //confidence / uncertainty
                if (result[0].Confidence == null)
                {
                    var confidence_promote = result[0].LocationDataList.Where(e => e.Confidence != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                    if (confidence_promote != null)
                    {
                        double confidence;
                        if( double.TryParse(confidence_promote.Confidence.Text, out confidence) )
                        {
                            result[0].Confidence = confidence;
                            result[0].Uncertainty = 100 - confidence;
                            trackingCollection.Add("Confidence");
                        }
                        else if( !string.IsNullOrEmpty(confidence_promote.Confidence.Text)) //capture log case if there is data, but it didn't parse as expected.
                        {
                            _logger.LogWarning($"Confidence failed to parse to double - value: {confidence_promote.Confidence.Text} - Callid: {result[0].CallIdentifier}");
                        }
                    }
                }

                //Carrier
                if (string.IsNullOrEmpty(result[0].Carrier))
                {
                    var carrier_promote = result[0].LocationDataList.Where(e => e.ProviderInfo != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                    if (carrier_promote != null)
                    {
                        result[0].Carrier = carrier_promote.ProviderInfo.DataProviderString;
                        trackingCollection.Add("Carrier");
                    }
                }

                //Geo - Lat/Long 
                //Promotion logic works against all sources of Geo shapes - parsing out based on priority of data than calculation of center point based on said geo shape.
                //Priority is: Point, Circle*, Ellipse*, Rectangle* - arcband is currently not processed. (*: includes 2D, than 3D)
                if ( result[0].Location == null )
                {
                    do
                    {
                        //First, check to see if there is Point data available.
                        var location_promote = result[0].LocationDataList.Where(e => e.Point != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                        if (location_promote != null)
                        {
                            result[0].Location = new CallLocation(location_promote.Point.Latitude, location_promote.Point.Longitude);
                            trackingCollection.Add("Location");
                            break;
                        }

                        //Circle
                        location_promote = result[0].LocationDataList.Where(e => e.Circle != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                        if (location_promote != null)
                        {
                            result[0].Location = new CallLocation(location_promote.Circle.Point.Latitude, location_promote.Circle.Point.Longitude);
                            trackingCollection.Add("Location");
                            break;
                        }


                        //Sphere
                        location_promote = result[0].LocationDataList.Where(e => e.Sphere != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                        if (location_promote != null)
                        {
                            result[0].Location = new CallLocation(location_promote.Sphere.Point.Latitude, location_promote.Sphere.Point.Longitude);
                            trackingCollection.Add("Location");
                            break;
                        }

                        //Ellipse
                        location_promote = result[0].LocationDataList.Where(e => e.Ellipse != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                        if (location_promote != null)
                        {
                            result[0].Location = new CallLocation(location_promote.Ellipse.Point.Latitude, location_promote.Ellipse.Point.Longitude);
                            trackingCollection.Add("Location");
                            break;
                        }

                        //Ellipsoid
                        location_promote = result[0].LocationDataList.Where(e => e.Ellipsoid != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                        if (location_promote != null)
                        {
                            result[0].Location = new CallLocation(location_promote.Ellipsoid.Point.Latitude, location_promote.Ellipsoid.Point.Longitude);
                            trackingCollection.Add("Location");
                            break;
                        }

                        //Ellipsoid
                        location_promote = result[0].LocationDataList.Where(e => e.Ellipsoid != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                        if (location_promote != null)
                        {
                            result[0].Location = new CallLocation(location_promote.Ellipsoid.Point.Latitude, location_promote.Ellipsoid.Point.Longitude);
                            trackingCollection.Add("Location");
                            break;
                        }

                        //Polygon
                        location_promote = result[0].LocationDataList.Where(e => e.Polygon != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                        if (location_promote != null)
                        {
                            ElasticSearch.Entities.Point centerPoint = location_promote.Polygon.CalculateCenterPointBasedOnPoints();

                            if (centerPoint == null)
                            {
                                _logger.LogError($"Getting empty Polygon points during data promotion for {result[0].CallIdentifier}");
                            }
                            else
                            {
                                result[0].Location = new CallLocation(centerPoint.Latitude, centerPoint.Longitude);
                                trackingCollection.Add("Location");
                                break;
                            }
                        }
                        //Prism
                        location_promote = result[0].LocationDataList.Where(e => e.Prism != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                        if (location_promote != null)
                        {
                            ElasticSearch.Entities.Point centerPoint = location_promote.Prism.CalculateCenterPointBasedOnPoints();

                            if (centerPoint == null)
                            {
                                _logger.LogError($"Getting empty Prism points during data promotion for {result[0].CallIdentifier}");
                            }
                            else
                            {
                                result[0].Location = new CallLocation(centerPoint.Latitude, centerPoint.Longitude);
                                trackingCollection.Add("Location");
                                break;
                            }
                        }

                        //Arc
                        location_promote = result[0].LocationDataList.Where(e => e.ArcBand != null).OrderByDescending(e => e.TimeStamp).FirstOrDefault();
                        if (location_promote != null)
                        {
                            //result[0].Location = new CallLocation(location_promote.ArcBand.Point.Latitude, location_promote.ArcBand.Point.Longitude);
                            //trackingCollection.Add("Location");
                            _logger.LogWarning($"Only geo shape found is a ARCBAND, currently not parsing the center point. Callid: {result[0].CallIdentifier}");
                            break;
                        }

                    }
                    while (false);
                }


                //Determine if the Call Mobility type has previously been captured, if "Unknown", try re-processing, as Unknown is the default state. (related to INFO-2236)
                if (string.IsNullOrEmpty(result[0].CallMobilityType) || result[0].CallMobilityType == "Unknown")
                {
                    //Logic can be based on the known Service information - instead of doing mapping back from COS - based on the lookup, simple checking the "ServiceType" of the Location data is enough.
                    //Change in logic from source, condition isn't hit unless CallMobility is empty (i.e. set by message) and applied directly to the Call Root record, where children are set later

                    var serviceType_Promote = result[0].LocationDataList.Where(e => e.ServiceInfo != null && !string.IsNullOrEmpty(e.ServiceInfo.ServiceType)).OrderByDescending(e => e.TimeStamp).FirstOrDefault();

                    //setting the CallMobility Type (Landline, Wireless, VOIP)
                    if (serviceType_Promote == null)
                    {
                        callmobilityType = CallMobilityType.RecordNotFound;
                    }
                    else if (string.IsNullOrEmpty(result[0].FinalCos))
                    {
                        callmobilityType = CallMobilityType.Unknown;
                    }
                    else
                    {
                        string mobilityString = Entities.ClassOfService.GetCallMobilityType(serviceType_Promote.ServiceInfo.ServiceType);

                        if( string.IsNullOrEmpty(mobilityString))
                        {
                            _logger.LogWarning($"Unexpected CallMobilityType unknown case.  Please review. {result[0].CallIdentifier}, ServiceType: {serviceType_Promote.ServiceInfo.ServiceType}");
                            callmobilityType = CallMobilityType.Unknown;
                        }
                        else
                        {
                            callmobilityType = Enum.Parse<CallMobilityType>(mobilityString);
                        }
                    }
                    result[0] = SetCallMobilityType(result[0], callmobilityType, false);

                    trackingCollection.Add("CallMobilityType");
                }
            }
            //only require updating logic if there was updated fields.
            if (trackingCollection.Count > 0)
            {
                //used to track the first occurence of a child record of PSAP and agent data for CallMobilityType related settings
                Dictionary<string, List<string>> psapVsAgents = new Dictionary<string, List<string>>();

                //Last, populate the updated fields to all callsummarrecords / sync with Call Summary Root.
                //interation approach skipping over the root / index 0 record.
                for (int i = 1; i < result.Count; i++)
                {
                    if (trackingCollection.Contains("CallbackNumber"))
                    {
                        result[i].CallBackNumber = result[0].CallBackNumber;
                    }
                    if (trackingCollection.Contains("FinalCos"))
                    {
                        result[i].FinalCos = result[0].FinalCos;
                    }
                    if (trackingCollection.Contains("OriginalCos"))
                    {
                        result[i].OriginalCos = result[0].OriginalCos;
                    }
                    if (trackingCollection.Contains("Address"))
                    {
                        result[i].Address = result[0].Address;
                    }
                    if (trackingCollection.Contains("Confidence"))
                    {
                        result[i].Confidence = result[0].Confidence;
                        result[i].Uncertainty = result[0].Uncertainty;
                    }
                    if (trackingCollection.Contains("Carrier"))
                    {
                        result[i].Carrier = result[0].Carrier;
                    }
                    if (trackingCollection.Contains("Location"))
                    {
                        result[i].Location = result[0].Location;
                    }
                    //Logic for Call Mobility type is based on UpdateAliFieldsInCallsummary, simplified to match ADR data availability
                    if(trackingCollection.Contains("CallMobilityType"))
                    {
                        //Checking to confirm PSAP is defined
                        if (!string.IsNullOrEmpty(result[i].PsapName))
                        {
                            //Confirming first child with a valid PSAP/Agent record
                            if (psapVsAgents.ContainsKey(result[i].PsapName) && !string.IsNullOrEmpty(result[i].AgentName) && !psapVsAgents[result[i].PsapName].Contains(result[i].AgentName))
                            {
                                psapVsAgents[result[i].PsapName].Add(result[i].AgentName);
                                //set the Call Mobility type 
                                result[i] = SetCallMobilityType(result[i], callmobilityType, false);
                            }
                            else if (!psapVsAgents.ContainsKey(result[i].PsapName)) //confirming the First PSAP record
                            {
                                result[i] = SetCallMobilityType(result[i], callmobilityType, false);

                                List<string> agents = new List<string>();
                                psapVsAgents.Add(result[i].PsapName, agents);
                            }
                        }

                    }

                }
            }

        }
                
        private void PopulateAliEventsInIndex(RootEvent existingEventRoot)
        {
            var aliEvents = existingEventRoot.Events.Where(e => e.aliQuery != null||e.aliResponse!=null).OrderBy(e => e.timestamp).ToList();
            foreach(EventLog aliEvent in aliEvents)
            {
                //Save this to ali index
            }
        }

        /// <summary>
        /// Calculates the Answered*Than fields
        /// </summary>
        /// <param name="callsummary"></param>
        /// <returns></returns>
        private CallSummary SetCallToAnswer(CallSummary callsummary)
        {
            //In case check to confirm the Call summary record is defined.
            if(callsummary == null)
            {
                return callsummary;
            }

            if (callsummary.TimeToAnswerInSeconds <= 10)
            {
                callsummary.SystemAnsweredWithin10s =
                    callsummary.SystemAnsweredWithin15s = callsummary.SystemAnsweredWithin20s = callsummary.SystemAnsweredWithin40s = 1;
                callsummary.SystemAnsweredMoreThan10s = callsummary.SystemAnsweredMoreThan20s = callsummary.SystemAnsweredMoreThan40s = 0;
            }
            else if (callsummary.TimeToAnswerInSeconds > 10 && callsummary.TimeToAnswerInSeconds <= 15)
            {
                callsummary.SystemAnsweredWithin15s = callsummary.SystemAnsweredWithin20s = callsummary.SystemAnsweredWithin40s = callsummary.SystemAnsweredMoreThan10s = 1;
                callsummary.SystemAnsweredWithin10s = callsummary.SystemAnsweredMoreThan40s = callsummary.SystemAnsweredMoreThan20s = 0;
            }
            else if (callsummary.TimeToAnswerInSeconds > 15 && callsummary.TimeToAnswerInSeconds <= 20)
            {
                callsummary.SystemAnsweredWithin40s = callsummary.SystemAnsweredWithin20s = callsummary.SystemAnsweredMoreThan10s = 1;
                callsummary.SystemAnsweredWithin10s = callsummary.SystemAnsweredWithin15s = callsummary.SystemAnsweredMoreThan20s = callsummary.SystemAnsweredMoreThan40s = 0;
            }
            else if (callsummary.TimeToAnswerInSeconds > 20 && callsummary.TimeToAnswerInSeconds <= 40)
            {
                callsummary.SystemAnsweredWithin40s = callsummary.SystemAnsweredMoreThan10s = callsummary.SystemAnsweredMoreThan20s = 1;
                callsummary.SystemAnsweredWithin10s = callsummary.SystemAnsweredWithin15s = callsummary.SystemAnsweredWithin20s = callsummary.SystemAnsweredMoreThan40s = 0;
            }
            else
            {
                callsummary.SystemAnsweredMoreThan40s = callsummary.SystemAnsweredMoreThan10s = callsummary.SystemAnsweredMoreThan20s = 1;
                callsummary.SystemAnsweredWithin10s =
                    callsummary.SystemAnsweredWithin15s = callsummary.SystemAnsweredWithin20s = callsummary.SystemAnsweredWithin40s = 0;
            }

            return callsummary;
        }

        /// <summary>
        /// Calculates the Non Emergency Answered*Than fields
        /// </summary>
        /// <param name="callsummary"></param>
        /// <returns></returns>
        private CallSummary SetNonEmergencyCallToAnswer(CallSummary callsummary)
        {
            //In case check to confirm the Call summary record is defined.
            if (callsummary == null)
            {
                return callsummary;
            }

            if (callsummary.NonEmergencyTimeToAnswerInSeconds <= 10)
            {
                callsummary.NonEmergencyAnsweredWithin10s =
                    callsummary.NonEmergencyAnsweredWithin15s = callsummary.NonEmergencyAnsweredWithin20s = callsummary.NonEmergencyAnsweredWithin40s = 1;
                callsummary.NonEmergencyAnsweredMoreThan10s = callsummary.NonEmergencyAnsweredMoreThan20s = callsummary.NonEmergencyAnsweredMoreThan40s = 0;
            }
            else if (callsummary.NonEmergencyTimeToAnswerInSeconds > 10 && callsummary.NonEmergencyTimeToAnswerInSeconds <= 15)
            {
                callsummary.NonEmergencyAnsweredWithin15s = callsummary.NonEmergencyAnsweredWithin20s = callsummary.NonEmergencyAnsweredWithin40s = callsummary.NonEmergencyAnsweredMoreThan10s = 1;
                callsummary.NonEmergencyAnsweredWithin10s = callsummary.NonEmergencyAnsweredMoreThan40s = callsummary.NonEmergencyAnsweredMoreThan20s = 0;
            }
            else if (callsummary.NonEmergencyTimeToAnswerInSeconds > 15 && callsummary.NonEmergencyTimeToAnswerInSeconds <= 20)
            {
                callsummary.NonEmergencyAnsweredWithin40s = callsummary.NonEmergencyAnsweredWithin20s = callsummary.NonEmergencyAnsweredMoreThan10s = 1;
                callsummary.NonEmergencyAnsweredWithin10s = callsummary.NonEmergencyAnsweredWithin15s = callsummary.NonEmergencyAnsweredMoreThan20s = callsummary.NonEmergencyAnsweredMoreThan40s = 0;
            }
            else if (callsummary.NonEmergencyTimeToAnswerInSeconds > 20 && callsummary.NonEmergencyTimeToAnswerInSeconds <= 40)
            {
                callsummary.NonEmergencyAnsweredWithin40s = callsummary.NonEmergencyAnsweredMoreThan10s = callsummary.NonEmergencyAnsweredMoreThan20s = 1;
                callsummary.NonEmergencyAnsweredWithin10s = callsummary.NonEmergencyAnsweredWithin15s = callsummary.NonEmergencyAnsweredWithin20s = callsummary.NonEmergencyAnsweredMoreThan40s = 0;
            }
            else
            {
                callsummary.NonEmergencyAnsweredMoreThan40s = callsummary.NonEmergencyAnsweredMoreThan10s = callsummary.NonEmergencyAnsweredMoreThan20s = 1;
                callsummary.NonEmergencyAnsweredWithin10s =
                    callsummary.NonEmergencyAnsweredWithin15s = callsummary.NonEmergencyAnsweredWithin20s = callsummary.NonEmergencyAnsweredWithin40s = 0;
            }

            return callsummary;
        }

        private List<CallSummary> PopulateAllAnswerRecords(RootEvent existingEventRoot, List<CallSummary> result)
        {
            var answerEvents = existingEventRoot.Events.Where(e => e.answer != null).OrderBy(e => e.timestamp).ToList();
            var routeOrTransferEvents = existingEventRoot.Events.Where(e => e.route != null || e.transferCall!=null).OrderBy(e => e.timestamp).ToList();
            var mediaEvents = existingEventRoot.Events.Where(e => e.media != null).OrderBy(e => e.timestamp).ToList();
            int count = 1;
            foreach (var answerEvent in answerEvents)
            {
                var holdEvents = existingEventRoot.Events.Where(e => e.hold != null && e.agencyOrElement == answerEvent.agencyOrElement && e.agent == answerEvent.agent && e.timestamp > answerEvent.timestamp && e.hold.MediaLabel==answerEvent.answer.MediaLabel).OrderBy(e => e.timestamp).ToList();
                var holdRetrievedEvents = existingEventRoot.Events.Where(e => e.holdRetrieved != null && e.agencyOrElement == answerEvent.agencyOrElement && e.agent == answerEvent.agent && e.timestamp > answerEvent.timestamp && e.holdRetrieved.MediaLabel == answerEvent.answer.MediaLabel).OrderBy(e => e.timestamp).ToList();
                var endMediaEvents = existingEventRoot.Events.Where(e => e.endMedia != null && e.agencyOrElement==answerEvent.agencyOrElement && e.agent==answerEvent.agent && e.timestamp>answerEvent.timestamp && e.endMedia.MediaLabel.Contains(answerEvent.answer.MediaLabel)).OrderBy(e => e.timestamp).ToList();
                var isSystemAnswer = !IsValidAgency(answerEvent.agencyOrElement);
                string mediaLabelTruncated;
                if (!string.IsNullOrEmpty(answerEvent.answer.MediaLabel) && answerEvent.answer.MediaLabel.Contains("@"))
                    mediaLabelTruncated = answerEvent.answer.MediaLabel.Substring(0, answerEvent.answer.MediaLabel.IndexOf('@'));
                else
                    mediaLabelTruncated = answerEvent.answer.MediaLabel;

                var newElkMasterRecord = result.FirstOrDefault().Clone() as CallSummary;
                newElkMasterRecord.Id = answerEvent.callIdentifier +"_"+ count.ToString();
                newElkMasterRecord.CallDetailsIndex = count;
                count++;
                newElkMasterRecord.PsapName = GetValidAgency(answerEvent.agencyOrElement);
                newElkMasterRecord.AgentName = answerEvent.agent;
                newElkMasterRecord.CallType = result.FirstOrDefault().CallType;
                newElkMasterRecord.CallState = result.FirstOrDefault().CallState;
                newElkMasterRecord.IsTransferred = newElkMasterRecord.IsCallback = newElkMasterRecord.IsInternalTransferCall = newElkMasterRecord.IsAlternativeRoute = 0;   //re-initializing properities to account for re-importing / initial seed from case of Call Summary.
                newElkMasterRecord.TimeStamp = answerEvent.timestamp;
                newElkMasterRecord.CallArrivedSystem = !isSystemAnswer ? GetCallArrivedFromRouteAndTransfers(routeOrTransferEvents, answerEvent) : null;
                newElkMasterRecord.CallPresented = !isSystemAnswer ? GetCallPresentedTimestamp(mediaEvents, answerEvent) : null;
                newElkMasterRecord.CallAnswered = answerEvent.timestamp;
                newElkMasterRecord.MediaLabel = mediaLabelTruncated;
                if (endMediaEvents.Any())
                    newElkMasterRecord.CallReleased = endMediaEvents.FirstOrDefault().timestamp;
                newElkMasterRecord.AnsweredBySystem = (isSystemAnswer ? 1 : 0);

                var existingPsapWithAgentRecord = (string.IsNullOrEmpty(newElkMasterRecord.PsapName) || string.IsNullOrEmpty(newElkMasterRecord.AgentName) ||
                                      result.Any(cr => cr.PsapName == newElkMasterRecord.PsapName && cr.AgentName !=null));
                if (!isSystemAnswer)
                {
                    
                    if (newElkMasterRecord.CallPresented.HasValue)
                    {
                        //Updating child record fields for time to answer
                        newElkMasterRecord.AgentTimeToAnswerInSeconds = (newElkMasterRecord.CallAnswered.GetValueOrDefault() - newElkMasterRecord.CallPresented.GetValueOrDefault()).TotalSeconds;
                        if (newElkMasterRecord.AgentTimeToAnswerInSeconds <= 10)
                        {
                            newElkMasterRecord.AgentAnsweredWithin10s = newElkMasterRecord.AgentAnsweredWithin20s =
                                newElkMasterRecord.AgentAnsweredWithin15s = newElkMasterRecord.AgentAnsweredWithin40s = 1;
                            newElkMasterRecord.AgentAnsweredMoreThan10s = newElkMasterRecord.AgentAnsweredMoreThan20s = newElkMasterRecord.AgentAnsweredMoreThan40s = 0;
                        }
                        else if (newElkMasterRecord.AgentTimeToAnswerInSeconds > 10 && newElkMasterRecord.AgentTimeToAnswerInSeconds <= 15)
                        {
                            newElkMasterRecord.AgentAnsweredWithin15s = newElkMasterRecord.AgentAnsweredWithin20s = newElkMasterRecord.AgentAnsweredWithin40s = newElkMasterRecord.AgentAnsweredMoreThan10s = 1;
                            newElkMasterRecord.AgentAnsweredWithin10s = newElkMasterRecord.AgentAnsweredMoreThan20s = newElkMasterRecord.AgentAnsweredMoreThan40s = 0;
                        }
                        else if (newElkMasterRecord.AgentTimeToAnswerInSeconds > 15 && newElkMasterRecord.AgentTimeToAnswerInSeconds <= 20)
                        {
                            newElkMasterRecord.AgentAnsweredWithin20s = newElkMasterRecord.AgentAnsweredWithin40s = newElkMasterRecord.AgentAnsweredMoreThan10s = 1;
                            newElkMasterRecord.AgentAnsweredWithin10s = newElkMasterRecord.AgentAnsweredWithin15s = newElkMasterRecord.AgentAnsweredMoreThan20s = newElkMasterRecord.AgentAnsweredMoreThan40s = 0;
                        }
                        else if (newElkMasterRecord.AgentTimeToAnswerInSeconds > 20 && newElkMasterRecord.AgentTimeToAnswerInSeconds <= 40)
                        {
                            newElkMasterRecord.AgentAnsweredMoreThan20s = newElkMasterRecord.AgentAnsweredWithin40s = newElkMasterRecord.AgentAnsweredMoreThan10s = 1;
                            newElkMasterRecord.AgentAnsweredWithin10s = newElkMasterRecord.AgentAnsweredWithin15s = newElkMasterRecord.AgentAnsweredWithin20s = newElkMasterRecord.AgentAnsweredMoreThan40s = 0;

                        }
                        else
                        {
                            newElkMasterRecord.AgentAnsweredMoreThan10s = newElkMasterRecord.AgentAnsweredMoreThan20s = newElkMasterRecord.AgentAnsweredMoreThan40s = 1;
                            newElkMasterRecord.AgentAnsweredWithin10s = newElkMasterRecord.AgentAnsweredWithin15s = newElkMasterRecord.AgentAnsweredWithin20s = newElkMasterRecord.AgentAnsweredWithin40s = 0;
                        }
                        

                        if (result.FirstOrDefault().IsEmergency)
                        {
                            newElkMasterRecord.HoldTimeInSeconds = GetHoldTime(holdEvents, holdRetrievedEvents);
                            if (newElkMasterRecord.CallReleased != null)
                                newElkMasterRecord.TalkTimeInSeconds = (newElkMasterRecord.CallReleased.GetValueOrDefault() - answerEvent.timestamp).TotalSeconds - newElkMasterRecord.HoldTimeInSeconds;
                            else
                                newElkMasterRecord.TalkTimeInSeconds = null;

                            if (result.FirstOrDefault().HoldTimeInSeconds == null)
                                result.FirstOrDefault().HoldTimeInSeconds = newElkMasterRecord.HoldTimeInSeconds;
                            else
                                result.FirstOrDefault().HoldTimeInSeconds += newElkMasterRecord.HoldTimeInSeconds;
                            if (newElkMasterRecord.CallArrivedSystem.HasValue && !existingPsapWithAgentRecord)
                                newElkMasterRecord.PsapTimeToAnswerInSeconds = (answerEvent.timestamp - newElkMasterRecord.CallArrivedSystem.GetValueOrDefault()).TotalSeconds;

                            if (result.FirstOrDefault().TimeToAnswerInSeconds == null)
                            {
                                result.FirstOrDefault().TimeToAnswerInSeconds = (newElkMasterRecord.CallAnswered.GetValueOrDefault() - result.FirstOrDefault().StartCallTime.GetValueOrDefault()).TotalSeconds;
                                result.FirstOrDefault().CallAnswered = answerEvent.timestamp;

                                //settings the time calculations
                                result[0] = SetCallToAnswer(result[0]);
                            }

                        }
                        else
                        {
                            newElkMasterRecord.NonEmergencyHoldTimeInSeconds = GetHoldTime(holdEvents, holdRetrievedEvents);
                            if (newElkMasterRecord.CallReleased != null)
                                newElkMasterRecord.NonEmergencyTalkTimeInSeconds = (newElkMasterRecord.CallReleased.GetValueOrDefault() - answerEvent.timestamp).TotalSeconds - newElkMasterRecord.NonEmergencyHoldTimeInSeconds;
                            else
                                newElkMasterRecord.NonEmergencyTalkTimeInSeconds = null;
                            
                            if (result.FirstOrDefault().NonEmergencyHoldTimeInSeconds == null)
                                result.FirstOrDefault().NonEmergencyHoldTimeInSeconds = newElkMasterRecord.NonEmergencyHoldTimeInSeconds;
                            else
                                result.FirstOrDefault().NonEmergencyHoldTimeInSeconds += newElkMasterRecord.NonEmergencyHoldTimeInSeconds;
                            if (newElkMasterRecord.CallArrivedSystem.HasValue && !existingPsapWithAgentRecord)
                                newElkMasterRecord.NonEmergencyPsapTimeToAnswerInSeconds = (answerEvent.timestamp - newElkMasterRecord.CallArrivedSystem.GetValueOrDefault()).TotalSeconds;

                            if (result.FirstOrDefault().NonEmergencyTimeToAnswerInSeconds == null)
                            {
                                result.FirstOrDefault().NonEmergencyTimeToAnswerInSeconds = (newElkMasterRecord.CallAnswered.GetValueOrDefault() - result.FirstOrDefault().StartCallTime.GetValueOrDefault()).TotalSeconds;
                                result.FirstOrDefault().CallAnswered = answerEvent.timestamp;

                                //settings the time calculations
                                result[0] = SetNonEmergencyCallToAnswer(result[0]);
                            }


                        }

                    }
                }
                
                
                var existingPsapRecord = (string.IsNullOrEmpty(newElkMasterRecord.PsapName) ||
                                          result.Any(cr => cr.PsapName == newElkMasterRecord.PsapName));

                if (existingPsapRecord)
                {
                    //if another record exists for same psap make these fields 0 
                    newElkMasterRecord.AdminCall = newElkMasterRecord.EmergencyCall = newElkMasterRecord.UnknownCall
                        = newElkMasterRecord.AdminEmergencyCall = newElkMasterRecord.TandemCall = 0;
                    newElkMasterRecord.CallMobilityType = null;
                    newElkMasterRecord.WirelessType = newElkMasterRecord.VoipType = newElkMasterRecord.IsUnknownType =
                    newElkMasterRecord.LandlineType = newElkMasterRecord.SMSType = newElkMasterRecord.TDDType = newElkMasterRecord.RTTType=newElkMasterRecord.TDDChallenge = 0;
                }

                var existingOutboundCalls = existingEventRoot.Events.Where(oc =>oc.eventType== "OutboundCall" && oc.agencyOrElement == answerEvent.agencyOrElement && oc.agent == answerEvent.agent).ToList();
                if (existingOutboundCalls.Any())
                    newElkMasterRecord.IsOutbound = 1;

                var existingOutboundCallsSameMediaLabel = existingEventRoot.Events.Where(oc => oc.eventType == "OutboundCall" && oc.outboundCall.MediaLabel == answerEvent.answer.MediaLabel).ToList();
                if (existingOutboundCallsSameMediaLabel.Any())
                {
                    newElkMasterRecord.IsCallback = result.FirstOrDefault().IsCallback;
                    newElkMasterRecord.IsAbandonedCallback = result.FirstOrDefault().IsAbandonedCallback;
                    newElkMasterRecord.AgentCallbacknumber = result.FirstOrDefault().AgentCallbacknumber;
                }
                result.FirstOrDefault().PsapName = string.Empty;
                var psapNames = string.Join(",", result.Where(r => !string.IsNullOrEmpty(r.PsapName)).Select(r => r.PsapName));
                result.FirstOrDefault().PsapName = psapNames;
                result.Add(newElkMasterRecord);
            }
            return result;
        }

        private double GetHoldTime(List<EventLog> holdEvents, List<EventLog> holdRetrievedEvents)
        {
            double returnHoldTime = 0;
            if (holdEvents.Count == holdRetrievedEvents.Count)
            {
                for (int i = 0; i < holdEvents.Count; i++)
                {
                    returnHoldTime += (holdRetrievedEvents[i].timestamp - holdEvents[i].timestamp).TotalSeconds;
                }
            }
            else if (holdEvents.Count > 0 && holdRetrievedEvents.Count > 0)
            {
                returnHoldTime= (holdRetrievedEvents[0].timestamp - holdEvents[0].timestamp).TotalSeconds;
            }
            return returnHoldTime;
        }

        private DateTime? GetCallArrivedFromRouteAndTransfers(List<EventLog> existingRoutesOrTransfers, EventLog eventLog)
        {
            var arrivedRouteEventOrTransfer = existingRoutesOrTransfers.Where(r => IsValidAgency(r.agencyOrElement) && r.agencyOrElement == eventLog.agencyOrElement && r.agent == ".").FirstOrDefault();
            return arrivedRouteEventOrTransfer?.timestamp;
        }
        private DateTime? GetCallPresentedTimestamp(List<EventLog> existingMedia, EventLog eventLog)
        {
            var presentedMediaEvent = existingMedia.Where(r => IsValidAgency(r.agencyOrElement) && r.media.MediaLabel.Contains(eventLog.answer.MediaLabel)).FirstOrDefault();
            return presentedMediaEvent?.timestamp;
        }
        private string GetValidAgency(string agencyOrElement)
        {
            if (agencyOrElement.EndsWith("_A") || agencyOrElement.EndsWith("_B"))
                return null;
            else
                return agencyOrElement;
        }
        private bool IsValidAgency(string agencyOrElement)
        {
            return !agencyOrElement.EndsWith("_A") && !agencyOrElement.EndsWith("_B");
        }

        /// <summary>
        /// Processing the EventLog collection to find any Transfer events, and updates the approrpriate call Summary records.  
        /// Includes setting the state on the Root call Summary record.
        /// </summary>
        /// <param name="existingEventRoot">The collection of Events</param>
        /// <param name="result">Resulting CallSummary collection</param>
        /// <returns>Update Call Summary Collection</returns>
        private List<CallSummary> PopulateCallbackFields(RootEvent existingEventRoot, List<CallSummary> result)
        {
            //Logic to populate isCallback, isTransferred, isAbandonedCallback
            List<EventLog> transferEventsWithCallBack = existingEventRoot.Events.Where(e => e.transferCall != null).OrderBy(e => e.timestamp).ToList();
            
            int lastTransferredIndex = -1;
            //process the transfer type to append appropriate call summary properities
            foreach(EventLog transferEvent in transferEventsWithCallBack)
            {
                if (!string.IsNullOrEmpty(transferEvent.transferCall.TargetType) && transferEvent.transferCall.TargetType.ToLower().Equals("callback"))
                {
                    int firstRecordIndex = result.IndexOf(result.FirstOrDefault(cs => cs.MediaLabel == "_ML_" + transferEvent.transferCall.OriginatorMediaLabel));
                    result[0].IsCallback = 1;
                    result[0].AgentCallbacknumber= GetAgentCallbackNumber(transferEvent.transferCall.TransferTarget);
                    if (result[0].AbandonedState == 1)
                        result[0].IsAbandonedCallback = 1;
                    if (firstRecordIndex > -1)
                    {
                        //result[firstRecordIndex].IsTransferred = 1;     //NOT being set, as to avoid the Callback iteraction with Transfer flag, isCallBack tells the underlying behaviour for reports.
                        result[firstRecordIndex].IsCallback = 1;
                        result[firstRecordIndex].AgentCallbacknumber = GetAgentCallbackNumber(transferEvent.transferCall.TransferTarget);
                        if (result[0].AbandonedState == 1)
                            result[firstRecordIndex].IsAbandonedCallback = 1;
                    }
                }
                else   //find the specific Call Summary record that transfers the call.  Setting the transfer state.
                {
                    int firstRecordIndex = result.IndexOf(result.FirstOrDefault(cs => cs.MediaLabel == "_ML_" + transferEvent.transferCall.OriginatorMediaLabel));
                    if (firstRecordIndex > -1)
                    {
                        result[firstRecordIndex].IsTransferred = 1;
                        //Setting the Transfer Time, when the event occurred.
                        result[firstRecordIndex].CallTransferred = transferEvent.timestamp;
                        //setting the Time to Transfer
                        result[firstRecordIndex].TimeToTransferInSeconds = (result[firstRecordIndex].CallTransferred - result[firstRecordIndex].CallAnswered).GetValueOrDefault().TotalSeconds;

                        lastTransferredIndex = firstRecordIndex;
                    }
                    //setting the isTransferred state of events that are not callback (normal transfer events)
                    result[0].IsTransferred = 1;
                }
            }

            //After the above FOR loop, we need to determine which CallSummary was the start of the final leg of a transfer event.  
            //Logic is iterate on Call Summaries after the last transfer event and find the first with a valid PSAP that doesn't match the transfer event PSAP. 
            //This will exclude Callbacks and internal transfers, but capture the destination PSAP of the last leg.
            if (lastTransferredIndex > 0) //can't be 0, as that is the call summary root record.
            {
                string lastTransferPsap = result[lastTransferredIndex].PsapName;
                for (int i = lastTransferredIndex; i < result.Count; i++)
                {
                    if (!string.IsNullOrEmpty(result[i].PsapName) && result[i].PsapName != lastTransferPsap)
                    {
                        result[i].IsTransferred = 1;
                        break;  //exist after finding the first instance of the call, else it will hit up multiple entries into a possible call group
                    }
                }
            }

            

            return result;
        }

        /// <summary>
        /// Main Call Summary Index write function - completes the Tenant lookup logic and writting of the document to the index.
        /// </summary>
        /// <param name="resultCallsummary">Collection of Call Summary</param>
        /// <param name="clientCode">The customer client code to write to</param>
        /// <param name="tenantLookup">The lookup collection for mapped Tenants if defined.</param>
        private async Task InsertCallSummaries(List<CallSummary> resultCallsummary, string clientCode, Dictionary<string,string> tenantLookup)
        {
            //Logic here for PSAP check for indexPrefix 
            // If PSAP matching parameter definition for Multiple tenant configuration - reset indexPrefix - else, use indexPrefix as is.
            //  warning detection - if the PSAP exists but no mapping, log a warning for awareness.
            //string originalIndex = string.Empty;
            if (tenantLookup != null && tenantLookup.Count > 0)
            {
                Dictionary<string, List<CallSummary>> callSummaryClients = new Dictionary<string, List<CallSummary>>();

                //introduced due to isAlternativeRoute - to enable order enforcement of processing of empty PSAP case.
                List<string> processedPSAPKeys = new List<string>();

                //fetch all non empty PSAP names that are unique ...
                //Filters a unique PSAP PER call leg - based on unique grouping from PSAP and CallPresented, with the filtering against IsTransferred
                ////The grouping is required to capture if there is two seperate call legs / transfer events in the same PSAP.

                //Update the call to always exclude the Root Element from the available PSAP list - if the root is the only element with a valid PSAP name, the conditional following will capture that case. (ref: INFO-1420)
                List<CallSummary> psapList = resultCallsummary.Where(cs => !string.IsNullOrEmpty(cs.PsapName) && cs.IsTransferred == 1 && cs.CallDetailsIndex != 0).GroupBy(cs => new { cs.PsapName, cs.CallPresented }).Select(pn => pn.FirstOrDefault()).ToList();
                //List that tracks the PSAP idenfier to actual PSAP name, required for multiple inner transfer cases (i.e. PSAP1 -> PSAP2 -> PSAP1)
                Dictionary<string, string> psapLookupList = new Dictionary<string, string>();
                //If there is a transfer case of note
                if (psapList != null && psapList.Count > 0)
                {
                    //Displaying debug information based on the filtering list of Call Summary data.
                    //Base case, there is a transfer event and multiple Psap's are part of it.
                    if (psapList.Count > 1)
                    {
                        _logger.LogInformation($"Transfer Event with multiple Psaps, callid: {psapList[0].CallIdentifier} - PSAP List: {string.Join("|", psapList.Select(x => x.PsapName))}");

                        if( psapList.Count > 5)
                        {
                            _logger.LogWarning($"Detecting a high level of PSAPs {psapList.Count} - investigation maybe required. CallId {psapList[0].CallIdentifier}");
                        }
                    }
                    else if (psapList.Count == 1)   //there is a transfer event, but only a single PSAP defined.  No additional logic required.
                    {
                        _logger.LogWarning($"Transfer Event with only a single PSAP listed, callid: {psapList[0].CallIdentifier} - PSAP: {psapList[0].PsapName}");
                    }
                    else
                    {
                        _logger.LogWarning($"Transfer Event with no PSAP information detected, callid: {resultCallsummary[0].CallIdentifier}");
                    }

                    int psapIdx;
                    string psapLookup;
                    //initializing the listing object that will be used for the transferring logic.
                    foreach (CallSummary cs in psapList)
                    {
                        psapLookup = cs.PsapName;
                        //Adds a field PER call leg, appending a index count to a given PSAP if it is re-transferred to as part of the calls. 
                        psapIdx = 1;
                        while (callSummaryClients.Keys.Contains(psapLookup))
                        {
                            psapLookup = $"{psapLookup}_{psapIdx++}";
                        }
                       
                        callSummaryClients.Add(psapLookup, new List<CallSummary>());
                        //storing the source PSAP Name from the lookup, to enable easier handling of later logic for index and call summary generation.
                        if( !psapLookupList.ContainsKey(psapLookup))
                        {
                            psapLookupList.Add(psapLookup, cs.PsapName);
                        }
                    }

                    //Case can occur if there is only the single PSAP available outside the Root element comma list.
                    if( callSummaryClients.Count == 1 )
                    {
                        callSummaryClients[callSummaryClients.Keys.First()] = resultCallsummary;
                        _logger.LogWarning($"Single PSAP found for callid: {resultCallsummary[0].CallIdentifier}, PSAP: {callSummaryClients.Keys.First()}");
                    }
                    else if (callSummaryClients.Count == 0) //this case shouldn't occur naturally, however, logic present for this edge case - where there was no  PSAPs found.
                    {
                        callSummaryClients.Add(string.Empty, resultCallsummary);
                        _logger.LogWarning($"No Unique PSAP found for callid: {resultCallsummary[0].CallIdentifier}, PSAP List - {string.Join("|", psapList.Select(x => x.PsapName))}.");
                    }


                }
                else  //Create the single element call summary since it isn't a transfer case
                {
                    //retrieve the PSAP name from the avialable call summary data. - excluding the ROOT element as that is a summary element which can contain a comma seperated PSAP list. 
                    List<CallSummary> psapListSingle = resultCallsummary.Where(cs => !string.IsNullOrEmpty(cs.PsapName) && cs.CallDetailsIndex != 0).GroupBy(cs => cs.PsapName).Select(pn => pn.FirstOrDefault()).ToList();

                    if (psapListSingle != null && psapListSingle.Count > 0)
                    {
                        callSummaryClients.Add(psapListSingle[0].PsapName, resultCallsummary);
                    }
                    else  //no PSAP case - SHOULD be very rare.
                    {
                        //New backup check, if the PSAPList is empty, retrieve the PSAP and retrieve the first valid psap element. (comma seperate issue in historical data)
                        psapListSingle = resultCallsummary.Where(cs => !string.IsNullOrEmpty(cs.PsapName)).GroupBy(cs => cs.PsapName).Select(pn => pn.FirstOrDefault()).ToList();
                        if( psapListSingle != null && psapListSingle.Count > 0)
                        {
                            _logger.LogWarning($"PSAP found only in Root Element for callid: {resultCallsummary[0].CallIdentifier}, PSAP {resultCallsummary[0].PsapName}");
                            callSummaryClients.Add(psapListSingle[0].PsapName.Split(',')[0], resultCallsummary);
                        }
                        else
                        {
                            callSummaryClients.Add(string.Empty, resultCallsummary);
                            _logger.LogWarning($"No PSAP found for callid: {resultCallsummary[0].CallIdentifier}");
                        }
                    }

                }

                //string currentPsap = string.Empty;
                CallSummary rootCallSummary = null;

                //Tracks the unique PSAPs in the transfer and how many times they are processed.
                Dictionary<string, int> mappedPsapLookup = new Dictionary<string, int>();
                foreach(string psapLookupKey in psapLookupList.Keys)
                {
                    mappedPsapLookup.Add(psapLookupKey, 0);
                }

                processedPSAPKeys = callSummaryClients.Keys.ToList();
                string mappedPsap = string.Empty;
                if (callSummaryClients != null && callSummaryClients.Count > 1)     //no need for any processing if there is only ONE PSAP defined.
                {

                    //Used to track updating the time tracking of End Time and Start Time for each non-root Call summary record.
                    DateTime callReleasedPerPsap = DateTime.MinValue;
                    DateTime callArrivedPerPsap = DateTime.MinValue;

                    //Loop through the data and add the call summaries to the collection based on the current PSAP detected.
                    //CallSummary records are always in order of the call
                    foreach (CallSummary callsummary in resultCallsummary)
                    {
                        if (callsummary.CallDetailsIndex == 0)
                        {
                            rootCallSummary = callsummary;
                            continue;  // will do logic of Call Summary root addition after the initial seeding. 
                        }

                        //Updating the currently active PSAP in the call
                        if( !string.IsNullOrEmpty(callsummary.PsapName))
                        {
                            if (callSummaryClients.ContainsKey(callsummary.PsapName))
                            {

                                //grabbing the count of the current PSAP transferring events, to track different legs of a transfer in the same PSAP
                                int mappedPsapLoopupIndex = mappedPsapLookup[callsummary.PsapName];

                                if( mappedPsapLoopupIndex > 0 )
                                {
                                    mappedPsap = $"{callsummary.PsapName}_{mappedPsapLoopupIndex}";
                                }
                                else
                                {
                                    mappedPsap = callsummary.PsapName;
                                }
                                
                                //Case when there are multiple PSAPs in the callsummary collection, but they are NOT transfer events.  (i.e. Transfer -> ringgroup -> transfer)
                                //Make sure the index is avialable before incrementing the tracking state
                                if(callSummaryClients.ContainsKey($"{callsummary.PsapName}_{mappedPsapLoopupIndex + 1}"))
                                {
                                    //Tracking the number of times the given PSAP is transferred to - thus allowing a mapping to the unique call legs.
                                    mappedPsapLookup[callsummary.PsapName]++;
                                }
                            }
                        }

                        //Adding the Call Summary record to the appropriate element position.
                        if (!string.IsNullOrEmpty(mappedPsap))
                        {
                            callSummaryClients[mappedPsap].Add(callsummary);
                        }
                        else   
                        {
                            //Does occur on edge case, where the leading Call Summary records do not have a PSAP associated to them, termed a "Alternative Route" case.
                            _logger.LogError($"Alternative Route case detected.  PSAP not defined in Transfer Population logic for callId: {callsummary.CallIdentifier}");
                            //https://solacomtech.atlassian.net/browse/INFO-1366
                            //NEW LOGIC - adding in the empty look up entry, as there are few edge cases with this behaviour.
                            if ( !callSummaryClients.ContainsKey(string.Empty) )
                            {
                                callSummaryClients.Add(string.Empty, new List<CallSummary>());
                                //Fix: Need to insert the alternative route case at the top of the collection - as it is the leading call case, and if not there is a parsing issue with the Root it works with.
                                processedPSAPKeys.Insert(0, string.Empty);
                            }
                            //setting the flag to track these occurrences.
                            callsummary.IsAlternativeRoute = 1;
                            
                            callSummaryClients[string.Empty].Add(callsummary);
                        }
                    }

                    //resetting the count tracking for need for Call Summary Root id incrementing.
                    mappedPsapLookup = new Dictionary<string, int>();
                    foreach (string psapLookupKey in psapLookupList.Keys)
                    {
                        mappedPsapLookup.Add(psapLookupKey, 0);
                    }

                    //Loop which pushes the Call summary Root object to each PSAP in the call.   Updating the Call Summary as required for this seperation.
                    int currentIndex = 0;
                    foreach (string csKey in processedPSAPKeys)   //to reenforce ordering processing
                    {
                        //Case: Empty PSAP edge case, need to not re-create the Call Summary root on this case as it is not a full call leg, 
                        // but capturing of the Call Summary into the Root (emtpy) PSAP index.  No data is updated for these cases.
                        if( csKey == string.Empty)
                        { 
                            continue;
                        }

                        string esPSAPName = psapLookupList[csKey];

                        CallSummary newRoot = rootCallSummary.ShallowClone();
                        newRoot.TransferFrom = newRoot.TransferTo = string.Empty;   //required to reinit the transfer fields as later runs pull Call Summary data from ES for initializing.
                        newRoot.PsapName = esPSAPName;   //setting the PSAP name as the singular PSAP vs the comma seperated
                        newRoot.IsInternalTransferCall = 0; //setting the default state.
                        newRoot.Id = $"{rootCallSummary.CallIdentifier}_0"; //reintializing the Root identifier - required due to the append behaviour on a reprocess occurrence.
                        
                        newRoot.IsAbandoned = false;
                        newRoot.IsAbandonedCallback = newRoot.IsCallback = newRoot.AbandonedState = 0 ;
                        newRoot.IsOutbound = newRoot.IsCompleted = 0;
                        newRoot.HoldTimeInSeconds = newRoot.NonEmergencyHoldTimeInSeconds =  null;


                        //Setting the time fields on the call summary root record based on the first record.
                        if( callSummaryClients[csKey].Count > 0 )
                        { 
                            newRoot.TimeStamp = callSummaryClients[csKey][0].CallAnswered.GetValueOrDefault();
                            newRoot.CallAnswered = callSummaryClients[csKey][0].CallAnswered;
                                                        
                            newRoot.IsAbandonedCallback = callSummaryClients[csKey].Any(cs => cs.IsAbandonedCallback == 1) ? 1 : 0;
                            newRoot.IsCallback = callSummaryClients[csKey].Any(cs => cs.IsCallback == 1) ? 1 : 0;
                            newRoot.IsAbandoned = callSummaryClients[csKey].Any(cs => cs.IsAbandoned == true);
                            newRoot.AbandonedState = callSummaryClients[csKey].Any(cs => cs.AbandonedState == 1) ? 1 : 0;   //abandonedState is always 1 or 0 

                            newRoot.IsOutbound = callSummaryClients[csKey].Any(cs => cs.IsOutbound == 1) ? 1 : 0;

                            //Edge case capturing when the call has no callstate fields - logging the case for audit awareness
                            CallSummary callState_callsummary = callSummaryClients[csKey].Where(cs => !string.IsNullOrEmpty(cs.CallState)).FirstOrDefault();
                            if (callState_callsummary != null)
                            {
                                newRoot.CallState = callState_callsummary.CallState;
                            }
                            else
                            {
                                _logger.LogWarning($"Call Leg has no call state data, callid {rootCallSummary.CallIdentifier}, PSAP: {csKey}");
                            }

                            newRoot.IsCompleted = callSummaryClients[csKey].Any(cs => cs.IsCompleted == 1) ? 1 : 0;
                        }
                        else  //Logically this shouldn't happen with the data - however, making sure we capture it for future analysis of edge cases that can occur. (can only occur if the Call Root is the only record with PSAP information and no additional children exist)
                        {
                            _logger.LogWarning($"Call Leg has no data associated to it, skipping, callid {rootCallSummary.CallIdentifier}, PSAP: {csKey}");
                            continue;
                        }
                                                
                        //uniqueness requirement on the _id field, making sure it is always unique to the PSAP of the call.  (to handle non tenant configured transfer events)
                        int mappedPsapLoopupIndex = mappedPsapLookup[esPSAPName];
                        if(mappedPsapLoopupIndex > 0)   //Use case of having possible multiple Call Summary Roots, need to create a incremental index. 
                        {
                            newRoot.Id = $"{newRoot.Id}_{esPSAPName}_{mappedPsapLoopupIndex}";
                        }
                        else   
                        {
                            newRoot.Id = $"{newRoot.Id}_{esPSAPName}";
                        }
                        mappedPsapLookup[esPSAPName]++;

                        //Logic to set the Transfer to / From based on the position in the call
                        if (currentIndex == 0 && psapLookupList.Count > 0)
                        {
                            newRoot.TransferTo = psapLookupList.ElementAt(currentIndex + 1).Value;
                        }
                        else if (currentIndex == psapLookupList.Count - 1 && currentIndex > 0)
                        {
                            newRoot.TransferFrom = psapLookupList.ElementAt(currentIndex - 1).Value;
                        }
                        else
                        {
                            newRoot.TransferFrom = psapLookupList.ElementAt(currentIndex - 1).Value;
                            newRoot.TransferTo = psapLookupList.ElementAt(currentIndex + 1).Value; 
                        }

                        //Case, when there is a internal transfer event.
                        if (!string.IsNullOrEmpty(newRoot.TransferFrom) && string.Equals(newRoot.TransferFrom, newRoot.PsapName, StringComparison.OrdinalIgnoreCase) )
                        {
                            newRoot.IsInternalTransferCall = 1;
                        }
                        
                        callSummaryClients[csKey].Insert(0, newRoot);

                        //Looping through the resulting data set to set the End / Start Time for each transit leg
                        //get the Latest Call Released and First Call Arrived
                        callReleasedPerPsap = DateTime.MinValue;
                        callArrivedPerPsap = DateTime.MinValue;

                        var tempCallSummary = callSummaryClients[csKey].Where(c => c.CallReleased != null).OrderBy(co => co.CallReleased);
                        if( tempCallSummary != null && tempCallSummary.Count() > 0)     
                        {
                            callReleasedPerPsap = tempCallSummary.Last().CallReleased.GetValueOrDefault();
                        }
                        else
                        {
                            _logger.LogWarning($"Call Leg is missing Call Released data, callid: {rootCallSummary.CallIdentifier}, PSAP: {csKey}");
                        }
                        tempCallSummary = callSummaryClients[csKey].Where(c => c.CallArrivedSystem != null).OrderBy(co => co.CallArrivedSystem);
                        if( tempCallSummary != null && tempCallSummary.Count() > 0)
                        {
                            callArrivedPerPsap = tempCallSummary.First().CallArrivedSystem.GetValueOrDefault();
                        }
                        else
                        {
                            _logger.LogWarning($"Call Leg is missing Call Arrived data, callid {rootCallSummary.CallIdentifier}, PSAP: {csKey}");

                            _logger.LogWarning($"Retrieving root record timestamp instead of Call Arrived, callid {rootCallSummary.CallIdentifier}, PSAP: {csKey}");

                            callArrivedPerPsap = callSummaryClients[csKey][0].TimeStamp;
                        }
                        foreach (CallSummary callsummary in callSummaryClients[csKey])
                        {
                            callsummary.EndCallTime = callReleasedPerPsap;
                            callsummary.StartCallTime = callArrivedPerPsap;
                        }

                        //Final action - set the time calculations - needed to occur later as the starttime/endtime calculations needed to be compelted against the full call before these calculations can be applied.
                        if( callSummaryClients[csKey].Count > 0)    //To confirm the record exists - that there is a root summary event to the collection
                        {

                            if (callSummaryClients[csKey][0].IsEmergency)
                            {
                                callSummaryClients[csKey][0].TotalCallTimeInSeconds = (callSummaryClients[csKey][0].EndCallTime - callSummaryClients[csKey][0].StartCallTime).GetValueOrDefault().TotalSeconds;
                                callSummaryClients[csKey][0].TimeToAnswerInSeconds = (callSummaryClients[csKey][0].CallAnswered - callSummaryClients[csKey][0].CallArrivedSystem).GetValueOrDefault().TotalSeconds;

                                callSummaryClients[csKey][0] = SetCallToAnswer(newRoot);

                                var callsummarytemp = callSummaryClients[csKey].Where(cs => (cs.HoldTimeInSeconds != null));
                                if (callsummarytemp != null && callsummarytemp.Count() > 0)
                                {
                                    callSummaryClients[csKey][0].HoldTimeInSeconds = callsummarytemp.FirstOrDefault().HoldTimeInSeconds;
                                }
                            }
                            else
                            {
                                callSummaryClients[csKey][0].NonEmergencyTotalCallTimeInSeconds = (callSummaryClients[csKey][0].EndCallTime - callSummaryClients[csKey][0].StartCallTime).GetValueOrDefault().TotalSeconds;
                                callSummaryClients[csKey][0].NonEmergencyTimeToAnswerInSeconds = (callSummaryClients[csKey][0].CallAnswered - callSummaryClients[csKey][0].CallArrivedSystem).GetValueOrDefault().TotalSeconds;

                                callSummaryClients[csKey][0] = SetNonEmergencyCallToAnswer(newRoot);

                                var callsummarytemp = callSummaryClients[csKey].Where(cs => (cs.NonEmergencyHoldTimeInSeconds != null));
                                if( callsummarytemp != null && callsummarytemp.Count() > 0)
                                {
                                    callSummaryClients[csKey][0].NonEmergencyHoldTimeInSeconds = callsummarytemp.FirstOrDefault().NonEmergencyHoldTimeInSeconds;
                                }                                
                            }
                        }

                        currentIndex++;
                    }

                }
                //update the Non-transfer Call summary records with the time updates.  
                else if(callSummaryClients != null)     
                {
                    //populating the Time Properities to each Call Summary record for single PSAP case.
                    string singleCSKey = callSummaryClients.Keys.First();
                    DateTime? rootEndTime = callSummaryClients[singleCSKey][0].EndCallTime; //source: PouplateEndTime - last occurence EndCall eventLog.timestamp
                    foreach (CallSummary callSummary in callSummaryClients[singleCSKey])
                    {
                        callSummary.EndCallTime = rootEndTime;
                        //Note: starttime is already part of the core call summary template 
                    }
                }
                        
                //Finally, add the call summary data to the ES instance
                //originalIndex = indexPrefix;
                foreach (string psapClient in callSummaryClients.Keys)
                {
                    string esPSAPName = string.Empty;
                    
                    if( !String.IsNullOrWhiteSpace(psapClient) && psapLookupList.Count > 0)  //required due to the non-transfer cases
                    {
                        esPSAPName = psapLookupList[psapClient];
                    }     
                    else   //default case, single PSAP from the available list.
                    {
                        esPSAPName = psapClient;
                    }

                    _logger.LogInformation($"Insert for client {clientCode} root call summary with Call ID {callSummaryClients[psapClient][0].CallIdentifier}");

                    if( !string.IsNullOrEmpty(callSummaryClients[psapClient][0].Id) )
                    {
                        //MariaDB writes
                        if (callSummaryClients[psapClient] != null && callSummaryClients[psapClient].Count > 0)
                        {   
                            foreach (CallSummary callsummary in callSummaryClients[psapClient])
                            {
                                await _unitOfWork_InsightsData.SetCallSummary(clientCode, psapClient, callsummary);
                            }
                        }
                    }
                    else
                    {   //sending exception to allow the code to handle the setting of the tracking state in the DB to maintain the record for future review if required.
                        throw new Exception($"Processing of events failed for Call ID: {callSummaryClients[psapClient][0].CallIdentifier} due to empty Id field.");
                    }

                    
                }

            }
            else   //No lookup defined, so do the original logic as there is not multiple clients, therefore no need to do PSAP seperation logic (including transfer)
            {

                if (!string.IsNullOrEmpty(resultCallsummary[0].Id))
                {
                    //populating the Time Properities to each Call Summary record
                    foreach (CallSummary callsummary in resultCallsummary)
                    {
                        callsummary.EndCallTime = resultCallsummary[0].EndCallTime;         //source: PouplateEndTime - last occurence EndCall eventLog.timestamp
                        //Note: starttime is already part of the core call summary template 
                    }

                    _logger.LogInformation($"Insert to for client {clientCode} root call summary with Call ID {resultCallsummary[0].CallIdentifier}");

                    //MariaDB writes
                    if (resultCallsummary.Count > 0)
                    {
                        foreach (CallSummary callsummary in resultCallsummary)
                        {
                            await _unitOfWork_InsightsData.SetCallSummary(clientCode, string.Empty, callsummary);
                        }
                    }

                }
                else
                {   //sending exception to allow the code to handle the setting of the tracking state in the DB to maintain the record for future review if required.
                    throw new Exception($"Processing of events failed for Call ID: {resultCallsummary[0].CallIdentifier} due to empty Id field.");
                }
            }

        }

        private List<CallSummary> PopulateMessageFields(RootEvent existingEventRoot, List<CallSummary> result)
        {
            var messageEvents = existingEventRoot.Events.Where(e => e.message != null).OrderBy(e => e.timestamp).ToList();
            if (messageEvents != null && messageEvents.Count > 0)
            {
                Dictionary<string, List<string>> psapVsAgents = new Dictionary<string, List<string>>();
                for (int i = 0; i < result.Count; i++)
                {
                    bool psapPopulated = true;
                    bool agentPopulated = true;
                    if (!string.IsNullOrEmpty(result[i].PsapName) && result[i].CallDetailsIndex != 0)
                    {
                        if (psapVsAgents.ContainsKey(result[i].PsapName) && !string.IsNullOrEmpty(result[i].AgentName) && !psapVsAgents[result[i].PsapName].Contains(result[i].AgentName))
                        {
                            agentPopulated = false;
                            psapVsAgents[result[i].PsapName].Add(result[i].AgentName);
                        }
                        else if (!psapVsAgents.ContainsKey(result[i].PsapName))
                        {
                            psapPopulated = false;
                            List<string> agents = new List<string>();
                            psapVsAgents.Add(result[i].PsapName, agents);
                            if (!string.IsNullOrEmpty(result[i].AgentName))
                            {
                                agentPopulated = false;
                                psapVsAgents[result[i].PsapName].Add(result[i].AgentName);
                            }
                        }
                    }
                    bool isTddChallenge = false;
                    if (messageEvents.LastOrDefault().message.MessageType.ToLower() == "tdd")
                        isTddChallenge = IsTDDChallengeOnly(messageEvents);
                    if (!psapPopulated || result[i].CallDetailsIndex == 0)
                        result[i] = SetCallMobilityType(result[i], Enum.Parse<CallMobilityType>(messageEvents.LastOrDefault().message.MessageType),isTddChallenge);
                    else if (!agentPopulated)
                        result[i] = SetCallMobilityType(result[i], Enum.Parse<CallMobilityType>(messageEvents.LastOrDefault().message.MessageType),isTddChallenge);
                }
            }
            return result;
        }

        private bool IsTDDChallengeOnly(List<EventLog> messageEvents)
        {
            var outTdd = messageEvents.Where(e => e.message.MessageType.ToLower() == "tdd" && e.message.Direction.ToLower() == "out");
            var inTdd = messageEvents.Where(e => e.message.MessageType.ToLower() == "tdd" && e.message.Direction.ToLower() == "in");
            if ((inTdd != null && inTdd.Count() > 0) || (outTdd != null && outTdd.Count() > 2))
                return false;
            else
                return true;
        }


        private List<CallSummary> PopulateALIFields(RootEvent existingEventRoot, List<CallSummary> result, string clientCode)
        {
            var aliEvents = existingEventRoot.Events.Where(e => e.aliResponse != null).OrderBy(e => e.timestamp).ToList();
            if(aliEvents.Any())
            {
                EventLog firstAli=new EventLog();
                EventLog finalAli=new EventLog();
                foreach(EventLog aliEvent in aliEvents)
                {
                    bool isValidAli = true;
                    if (aliEvents.FirstOrDefault().aliResponse.Ali.ToLower().Contains("no response from cell steer")
                        || aliEvents.FirstOrDefault().aliResponse.Ali.ToLower().Contains("unable to transmit on any connection"))
                    {
                        isValidAli = false;
                    }
                    if (isValidAli)
                    {
                        if (string.IsNullOrEmpty(firstAli.callIdentifier))
                        {
                            firstAli = aliEvent;
                            finalAli = aliEvent;
                        }
                        else
                        {
                            finalAli = aliEvent;
                        }
                    }
                }
                if (string.IsNullOrEmpty(firstAli.callIdentifier))
                    return result;
                else if(firstAli.timestamp==finalAli.timestamp)    //Only one valid ALI
                {
                    Alidata aliRecord = ParseALIRecord(firstAli, clientCode);
                    if (aliRecord != null)
                    {
                        result = UpdateAliFieldsInCallsummary(aliRecord, result);
                    }
                }
                else            //Multiple Valid ALI Responses
                {
                    Alidata aliRecord = ParseALIRecord(firstAli, clientCode);
                    if (aliRecord != null)
                    {
                        result = UpdateAliFieldsInCallsummary(aliRecord, result);
                    }
                    aliRecord = ParseALIRecord(finalAli, clientCode);
                    if (aliRecord != null)
                    {
                        result = UpdateAliFieldsInCallsummary(aliRecord, result);
                    }
                }
            }
            return result;
        }

        private Alidata ParseALIRecord(EventLog aliResponse, string clientCode)
        {
            Alidata aliRecord = null;
            try
            {
                var aliParser = new AliParser(aliResponse.aliResponse.Ali, clientCode, _cache);
                aliRecord= aliParser.GetAlidata();
                aliRecord.Date = aliResponse.timestamp.Date.ToShortDateString()
                            .ToString();
                aliRecord.Time = aliResponse.timestamp.TimeOfDay.ToString();
            }
            catch (Exception e)
            {
                //TODO: Also add to ELK exception logs
                _logger.LogError(e, $"Exception in parsing aliResponse for {clientCode}");
            }
            return aliRecord;
        }

        private List<CallSummary> PopulateCallState(RootEvent existingEventRoot, List<CallSummary> result)
        {
            var endCallState = GetEndCallState(existingEventRoot);
            List<string> psaps = new List<string>();
            foreach(CallSummary elkRecord in result)
            {
                
                if(elkRecord.CallDetailsIndex == 0)
                {
                    elkRecord.CallState = endCallState.ToString();
                    if (endCallState == CallStates.Abandoned)
                    {
                        elkRecord.AbandonedState = 1;
                        elkRecord.IsAbandoned = true;
                        elkRecord.InProgress = 0;
                        elkRecord.IsCompleted = 1;
                    }

                    if (endCallState == CallStates.Completed)
                    {
                        elkRecord.IsCompleted = 1;
                        elkRecord.AbandonedState = elkRecord.InProgress = 0;
                    }

                    if (endCallState == CallStates.InProgress)
                    {
                        elkRecord.InProgress = 1;
                        elkRecord.AbandonedState = elkRecord.IsCompleted = 0;
                    }
                }
                else if(!psaps.Contains(elkRecord.PsapName))
                {
                    elkRecord.CallState = endCallState.ToString();
                    elkRecord.AbandonedState = endCallState == CallStates.Abandoned ? 1 : 0;
                    elkRecord.IsCompleted= endCallState == CallStates.Completed ? 1 : 0;
                    elkRecord.InProgress = endCallState == CallStates.InProgress ? 1 : 0;
                    elkRecord.IsAbandoned = endCallState == CallStates.Abandoned;
                    psaps.Add(elkRecord.PsapName);
                }
                else
                {
                    elkRecord.CallState = null;
                }
            }
            return result;
        }

        private List<CallSummary> PopulateEndTime(RootEvent existingEventRoot, List<CallSummary> result)
        {
            //Populating End Time, Time in seconds
            var endCalls = existingEventRoot.Events.Where(e => e.endCall != null).OrderBy(e => e.timestamp).ToList();
            if(result!=null && endCalls!=null && endCalls.Count>0)
            {
                result[0].EndCallTime = endCalls.LastOrDefault().timestamp;

                if (result[0].IsEmergency)
                    result[0].TotalCallTimeInSeconds = (result[0].EndCallTime.GetValueOrDefault() - result[0].StartCallTime.GetValueOrDefault()).TotalSeconds;
                else
                    result[0].NonEmergencyTotalCallTimeInSeconds = (result[0].EndCallTime.GetValueOrDefault() - result[0].StartCallTime.GetValueOrDefault()).TotalSeconds;
            }
            return result;
        }


        private CallStates GetEndCallState(RootEvent existingEventRoot)
        {
            var abandonedEndMedia = existingEventRoot.Events.Where(e => e.endMedia != null && e.endMedia.ResponseCode>256).OrderBy(e => e.timestamp).FirstOrDefault();
            var answerByAgent = existingEventRoot.Events.Where(e => e.media != null && !string.IsNullOrEmpty(e.agent) && e.agent !=".").OrderBy(e => e.timestamp).FirstOrDefault();
            if (answerByAgent == null || abandonedEndMedia != null)
            {
                return CallStates.Abandoned;
            }
            return CallStates.Completed;
        }



        /// <summary>
        /// Generates the root call summary record.  This is a copy of InsertESRootCallSummary without the ES write event.
        /// </summary>
        /// <param name="eventLog"></param>
        /// <returns></returns>
        private List<CallSummary> GenerateESRootCallSummary(EventLog eventLog)
        {
            var returnCallSummary = new List<CallSummary>();
            var callSummary = new CallSummary();
            if (eventLog != null && eventLog.startCall != null)   //StartCall
            {
                var callTypeEnum = GetCallTypesEnum(eventLog.startCall.CallType);
                callSummary.CallIdentifier = eventLog.callIdentifier;
                callSummary.IncidentIdentifier = eventLog.incidentIdentifier;
                callSummary.StartCallTime = eventLog.timestamp;
                callSummary.CallType = eventLog.startCall.CallType;
                callSummary.IsAdmin = callTypeEnum == CallTypes.Admin;
                callSummary.IsEmergency = callTypeEnum == CallTypes.Emergency;
                callSummary.IsTandem = callTypeEnum == CallTypes.Tandem;
                callSummary.IsAdminEmergency = callTypeEnum == CallTypes.AdminEmergency;
                callSummary.EmergencyCall = callTypeEnum == CallTypes.Emergency ? 1 : 0;
                callSummary.AdminCall = callTypeEnum == CallTypes.Admin ? 1 : 0;
                callSummary.TandemCall = callTypeEnum == CallTypes.Tandem ? 1 : 0;
                callSummary.AdminEmergencyCall = callTypeEnum == CallTypes.AdminEmergency ? 1 : 0;
                callSummary.UnknownCall = callTypeEnum == CallTypes.Unknown ? 1 : 0;
                callSummary.IsOutbound = callTypeEnum == CallTypes.Outbound ? 1 : 0;
                callSummary.EndCallTime = null;
                callSummary.TimeStamp = eventLog.timestamp;
                callSummary.Id = $"{eventLog.callIdentifier}_0";
                callSummary.CallDetailsIndex = 0;
                callSummary.IsTransferred = 0;
                callSummary.IsAlternativeRoute = 0;
                callSummary.IsInternalTransferCall = 0;                
                callSummary.IsCallback = 0;
                callSummary.IsAbandonedCallback = 0;  //Only startcalls here
                callSummary.AgentCallbacknumber = eventLog.startCall.Pani;      //TBD- Is it ANI or PANI?
            }
            else if (eventLog != null && eventLog.outboundCall != null)
            {
                var callTypeEnum = GetCallTypesEnum("Outbound");
                bool isCallback = false;
                if (!string.IsNullOrEmpty(eventLog.outboundCall.TargetType) &&
                    eventLog.outboundCall.TargetType.ToLower().Equals("callback"))
                    isCallback = true;
                callSummary.CallIdentifier = eventLog.callIdentifier;
                callSummary.IncidentIdentifier = eventLog.incidentIdentifier;
                callSummary.StartCallTime = eventLog.timestamp;
                callSummary.CallType = "Outbound";
                callSummary.IsAdmin = callTypeEnum == CallTypes.Admin;
                callSummary.IsEmergency = callTypeEnum == CallTypes.Emergency;
                callSummary.IsTandem = callTypeEnum == CallTypes.Tandem;
                callSummary.IsAdminEmergency = callTypeEnum == CallTypes.AdminEmergency;
                callSummary.EmergencyCall = callTypeEnum == CallTypes.Emergency ? 1 : 0;
                callSummary.AdminCall = callTypeEnum == CallTypes.Admin ? 1 : 0;
                callSummary.TandemCall = callTypeEnum == CallTypes.Tandem ? 1 : 0;
                callSummary.AdminEmergencyCall = callTypeEnum == CallTypes.AdminEmergency ? 1 : 0;
                callSummary.UnknownCall = callTypeEnum == CallTypes.Unknown ? 1 : 0;
                callSummary.IsOutbound = callTypeEnum == CallTypes.Outbound ? 1 : 0;
                callSummary.EndCallTime = null;
                callSummary.TimeStamp = eventLog.timestamp;
                callSummary.Id = $"{eventLog.callIdentifier}_0";
                callSummary.CallDetailsIndex = 0;
                callSummary.IsTransferred = 0;
                callSummary.IsAlternativeRoute = 0;
                callSummary.IsInternalTransferCall = 0;                
                callSummary.IsCallback = isCallback ? 1 : 0;
                callSummary.IsAbandonedCallback = callTypeEnum == CallTypes.Outbound && isCallback ? 1 : 0;  //TBD //No information about abandoned call here, so only outbound calls are considered here
                callSummary.AgentCallbacknumber = GetAgentCallbackNumber(eventLog.outboundCall.OutboundTarget);
            }
            else
            {
                _logger.LogWarning($"Failed in generating a ESRootCallSummary due to a empty eventLog (or .startcall/outboundcall not existing).");
            }


            SetCallState(callSummary, CallStates.InProgress);

            _logger.LogInformation($"Generated root call summary for in progress call with Call ID {callSummary.CallIdentifier}");

            returnCallSummary.Add(callSummary);
            return returnCallSummary;
        }
        
        /// <summary>
        /// Cleans up the return value or returns null if it doesnot exist
        /// </summary>
        /// <param name="transferTarget"></param>
        /// <returns></returns>
        private string GetAgentCallbackNumber(string transferTarget)
        {
            if (string.IsNullOrEmpty(transferTarget))
                return string.Empty;
            else
                return transferTarget.Trim();
        }
        
        private void SetCallState(CallSummary elkMasterRecord, CallStates state)
        {
            ///
            ///@TODO: this function should be part of the CallSummary object
            ///@TODO: It can be optimized to having a SWITCH statement since it is single state condition.
            ///
            elkMasterRecord.CallState = state.ToString();
            if (state == CallStates.Abandoned)
            {
                elkMasterRecord.AbandonedState = 1;
                elkMasterRecord.InProgress = elkMasterRecord.IsCompleted = 0;
            }

            if (state == CallStates.Completed)
            {
                elkMasterRecord.IsCompleted = 1;
                elkMasterRecord.AbandonedState = elkMasterRecord.InProgress = 0;
            }

            if (state == CallStates.InProgress)
            {
                elkMasterRecord.InProgress = 1;
                elkMasterRecord.AbandonedState = elkMasterRecord.IsCompleted = 0;
            }
        }
        private List<CallSummary> UpdateAliFieldsInCallsummary(Alidata aliRecord, List<CallSummary> result)
        {
            List<string> psaps = new List<string>();
            Dictionary<string, List<string>> psapVsAgents = new Dictionary<string, List<string>>();
            for (int i = 0; i < result.Count; i++)
            {
                bool psapPopulated = true;
                bool agentPopulated = true;
                if (!string.IsNullOrEmpty(result[i].PsapName) && result[i].CallDetailsIndex != 0)
                {
                    if (psapVsAgents.ContainsKey(result[i].PsapName) && !string.IsNullOrEmpty(result[i].AgentName) && !psapVsAgents[result[i].PsapName].Contains(result[i].AgentName))
                    {
                        agentPopulated = false;
                        psapVsAgents[result[i].PsapName].Add(result[i].AgentName);
                    }
                    else if (!psapVsAgents.ContainsKey(result[i].PsapName))
                    {
                        psapPopulated = false;
                        
                        List<string> agents = new List<string>();
                        psapVsAgents.Add(result[i].PsapName, agents);
                        if (!string.IsNullOrEmpty(result[i].AgentName))
                        {
                            agentPopulated = false;
                            psapVsAgents[result[i].PsapName].Add(result[i].AgentName);
                        }
                    }
                }
                //Updating the call back number from the first ALI
                if (!string.IsNullOrEmpty(aliRecord.Callbacknumber))
                    result[i].CallBackNumber = aliRecord.Callbacknumber;
                //Updating the Carrier
                if (!string.IsNullOrEmpty(aliRecord.Carrier))
                {
                    result[i].Carrier = aliRecord.Carrier;
                }
                //Updating the Original and Final Class of service
                if (!string.IsNullOrEmpty(aliRecord.Classofservice))
                {
                    if (string.IsNullOrEmpty(result[i].OriginalCos)) //only update if it's null
                    {
                        result[i].OriginalCos = aliRecord.Classofservice;
                        result[i].FinalCos = aliRecord.Classofservice;
                    }
                    else
                        result[i].FinalCos = aliRecord.Classofservice;
                }
                //Updating the CallMobility Type (Landline, Wireless, VOIP)
                if(!string.IsNullOrEmpty(result[i].CallMobilityType) && Enum.IsDefined(typeof(MessageType), result[i].CallMobilityType))
                {
                    //Do Nothing
                }
                else if (aliRecord.Type.Equals("9"))
                {
                    //NOTE: If it is a message type and ALI Response is not found, it would appear as a message
                    if (!psapPopulated || result[i].CallDetailsIndex == 0)
                        result[i] = SetCallMobilityType(result[i], CallMobilityType.RecordNotFound, false);
                    else if (!agentPopulated)
                        result[i] = SetCallMobilityType(result[i], CallMobilityType.RecordNotFound, false);
                }
                else if (aliRecord.Classofservice == null || !_cosSettings.Values.ContainsKey(aliRecord.Classofservice))
                {
                    if (!psapPopulated || result[i].CallDetailsIndex == 0)
                        result[i]=SetCallMobilityType(result[i], CallMobilityType.Unknown, false);
                    else if (!agentPopulated)
                        result[i] = SetCallMobilityType(result[i], CallMobilityType.Unknown, false);
                }
                else
                {
                    var cos = _cosSettings.Values[aliRecord.Classofservice];
                    var cosEnum = Enum.Parse<CallMobilityType>(cos);
                    if (!psapPopulated || result[i].CallDetailsIndex == 0)
                        result[i]=SetCallMobilityType(result[i], cosEnum, false);
                    else if (!agentPopulated)
                        result[i] = SetCallMobilityType(result[i], cosEnum, false);
                }
                //Updating the Location
                if (aliRecord.Latitude != null && aliRecord.Longitude != null)
                {
                    result[i].Location = new CallLocation(aliRecord.Latitude.Value, aliRecord.Longitude.Value);
                }
                //Updating Uncertainty
                if (aliRecord.Uncertainty != null)
                {
                    result[i].Uncertainty = aliRecord.Uncertainty.Value;
                }
                if (aliRecord.Esn != null)
                {
                    result[i].Esn = aliRecord.Esn.Value;
                }
                if (aliRecord.Address != null)
                {
                    if (aliRecord.StreetNumber != null && aliRecord.StreetNumber.Value!=0)
                        result[i].Address = $"{aliRecord.StreetNumber.Value} {aliRecord.Address}";
                    else
                        result[i].Address = aliRecord.Address;
                }
                if (aliRecord.Zipcode != null)
                {
                    result[i].Zipcode = aliRecord.Zipcode;
                }
                //Updating Confidence
                if (aliRecord.Confidence != null)
                {
                    result[i].Confidence = aliRecord.Confidence.Value;
                }
            }
            return result;
        }

        private CallSummary SetCallMobilityType(CallSummary elkMasterRecord, CallMobilityType callMobilityType, bool isTddChallengeOnly)
        {
            elkMasterRecord.CallMobilityType = callMobilityType.ToString();

            if (callMobilityType == CallMobilityType.Landline)
            {
                elkMasterRecord.LandlineType = 1;
                elkMasterRecord.VoipType = elkMasterRecord.WirelessType = elkMasterRecord.IsUnknownType = elkMasterRecord.SMSType = elkMasterRecord.TDDType = elkMasterRecord.RTTType = elkMasterRecord.NotFoundType = 0;
            }

            if (callMobilityType == CallMobilityType.Wireless)
            {
                elkMasterRecord.WirelessType = 1;
                elkMasterRecord.VoipType = elkMasterRecord.LandlineType = elkMasterRecord.IsUnknownType = elkMasterRecord.SMSType = elkMasterRecord.TDDType = elkMasterRecord.RTTType = elkMasterRecord.NotFoundType = 0;
            }

            if (callMobilityType == CallMobilityType.Voip)
            {
                elkMasterRecord.VoipType = 1;
                elkMasterRecord.LandlineType = elkMasterRecord.WirelessType = elkMasterRecord.IsUnknownType = elkMasterRecord.SMSType = elkMasterRecord.TDDType = elkMasterRecord.RTTType = elkMasterRecord.NotFoundType = 0;
            }

            if (callMobilityType == CallMobilityType.Unknown)
            {
                elkMasterRecord.IsUnknownType = 1;
                elkMasterRecord.VoipType = elkMasterRecord.WirelessType = elkMasterRecord.LandlineType = elkMasterRecord.SMSType = elkMasterRecord.TDDType = elkMasterRecord.RTTType = elkMasterRecord.NotFoundType = 0;
            }
            if (callMobilityType == CallMobilityType.RecordNotFound)
            {
                elkMasterRecord.NotFoundType = 1;
                elkMasterRecord.VoipType = elkMasterRecord.WirelessType = elkMasterRecord.IsUnknownType = elkMasterRecord.SMSType = elkMasterRecord.TDDType = elkMasterRecord.RTTType = elkMasterRecord.LandlineType = 0;
            }

            if (callMobilityType == CallMobilityType.SMS)
            {
                elkMasterRecord.SMSType = elkMasterRecord.WirelessType = 1;
                elkMasterRecord.VoipType = elkMasterRecord.LandlineType = elkMasterRecord.IsUnknownType = elkMasterRecord.TDDType = elkMasterRecord.RTTType = elkMasterRecord.NotFoundType = 0;
            }

            if (callMobilityType == CallMobilityType.TDD)
            {
                elkMasterRecord.TDDType = isTddChallengeOnly ? 0 : 1;
                elkMasterRecord.TDDChallenge = isTddChallengeOnly ? 1 : 0;
                elkMasterRecord.SMSType = elkMasterRecord.RTTType = 0;
            }

            if (callMobilityType == CallMobilityType.RTT)
            {
                elkMasterRecord.RTTType = elkMasterRecord.WirelessType = 1;
                elkMasterRecord.VoipType = elkMasterRecord.LandlineType = elkMasterRecord.SMSType = elkMasterRecord.TDDType = elkMasterRecord.IsUnknownType = elkMasterRecord.NotFoundType = 0;
            }
            return elkMasterRecord;
        }

        private CallTypes GetCallTypesEnum(string callType)
        {
            switch (callType?.ToLower())
            {
                case "admin":
                    return CallTypes.Admin;
                case "adminemergency":
                    return CallTypes.AdminEmergency;
                case "tandem":
                    return CallTypes.Tandem;
                case "e911":
                case "localng911":
                case "ng911":
                case "sr911":
                    return CallTypes.Emergency;
                case "outbound":
                    return CallTypes.Outbound;
                default:
                    return CallTypes.Unknown;
            }
        }

        private async Task<AgentSessionRecord> FindAgentSessionRecord(string mediaLabel, UserKeyData userKeyData)
        {
            var existing = await _unitOfWork.GetAgentSession(mediaLabel, userKeyData.ClientCode);
            return existing;
        }

        /// <summary>
        /// Retrieves any existing Events that have not been processed yet based on the age of the events.
        /// </summary>
        /// <param name="tenantLookupCollection">Tenant information</param>
        /// <param name="clientTimeZoneMapping">Client timezone lookup</param>
        /// <param name="numberOfHours">Age of the events</param>
        /// <remarks>This process now logs a Error state when events are found - expectation is Expiry cases should be edge cases, not expected functionality in the current call logging setup.</remarks>
        public async Task ProcessExpiredEvents( Dictionary<string, Dictionary<string, string>> tenantLookupCollection, Dictionary<string, NodaTime.DateTimeZone> clientTimeZoneMapping, int numberOfHours)
        {
            IList<CallInstance> expiredCallLookup;

            try
            {
                expiredCallLookup = await _unitOfWork.GetExpiredEventsCallId(numberOfHours);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EventsManager : ProcessExpiredEvents - Failed to retrieve the Expired Events.");
                return;
            }            

            if( expiredCallLookup != null && expiredCallLookup.Count > 0)
            {
                Dictionary<string, string> tenantLookup = null;
                List<EventLog> eventLogList;
                foreach (CallInstance callInstance in expiredCallLookup)
                {
                    //Make sure the Tenant lookup is defined for the found Client.
                    if (tenantLookupCollection != null && tenantLookupCollection.ContainsKey(callInstance.ClientId))
                    {
                        tenantLookup = tenantLookupCollection[callInstance.ClientId];
                    }
                    else
                    {
                        _logger.LogError($"EventsManager : ProcessExpiredEvents: Could not locate Tenant information for call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        try
                        {
                            await _unitOfWork.SetErrorEventState(callInstance.CallIdentifier, callInstance.ClientId, -1);
                        }
                        catch (Exception exDB)
                        {
                            _logger.LogError(exDB, $"EventsManager : ProcessExpiredEvents - Failed to update the Error state of the Event for Call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        }
                        continue;
                    }

                    //Next, make sure the Timezone configuration is available, halting process if it is not.
                    if (!clientTimeZoneMapping.ContainsKey(callInstance.ClientId))
                    {
                        _logger.LogError($"EventsManager: ProcessExpiredEvents - Client Code {callInstance.ClientId} does not have a configured timezone (ref: clientTimezoneMapping settings)");
                        try
                        {
                            await _unitOfWork.SetErrorEventState(callInstance.CallIdentifier, callInstance.ClientId, -1);
                        }
                        catch (Exception exDB)
                        {
                            _logger.LogError(exDB, $"EventsManager : ProcessExpiredEvents - Failed to update the Error state of the Event for Call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        }
                        continue;
                    }
                    

                    //Case - events are triggered as expired, but the call was previously processed.
                    //Condition can only occur if the trailing events appeared after a Events database cleaning process occurred.  Illustrating a event being presented well beyond expected functional time.
                    // - if the event appears when the Events are still present in queue, it will trigger a full call processing occurence as expected.
                    (bool callExists, DateTime dateCreated, long callSummaryCount) = await _unitOfWork_InsightsData.CallSummaryExists(callInstance.ClientId, callInstance.CallIdentifier);
                    if (callExists)
                    {
                        //Condition, if the existing records that have been previously processed did so outside the cleanup length.  Therefore, latest events can be missing full context, and no processing can occur.
                        if( dateCreated.AddHours(numberOfHours) <= callInstance.Date.Value)
                        {
                            _logger.LogError($"Call: {callInstance.ClientId}:{callInstance.CallIdentifier} was previously processed on {dateCreated} with resulting {callSummaryCount} call Summary records.  Halting processing and flagging Events.");
                            
                            try
                            {
                                await _unitOfWork.SetErrorEventState(callInstance.CallIdentifier, callInstance.ClientId, -1);
                            }
                            catch (Exception exDB)
                            {
                                _logger.LogError(exDB, $"EventsManager : ProcessExpiredEvents - Failed to update the Error state of the Event for Call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                            }

                            continue;
                        }
                    }

                    //retrieve all Events associated to the given Call Identifier, this has no bounds on max event Id.
                    eventLogList = await _unitOfWork.GetEventsForProcessing(callInstance.CallIdentifier, callInstance.ClientId, -1);

                    //Check - if any events are in EXPIRED state, indicating a previous failure to processing an expired call - do not process and re-set the expired state on all events.
                    bool expiredEvents = eventLogList.Any(ev => ev.DatabaseStateId == 5);
                    if( expiredEvents )
                    {
                        _logger.LogError($"Expired events have previously attempted expired processing.  Events state being update to expired(failure) for call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        try
                        {
                            await _unitOfWork.SetExpiredEventState(callInstance.CallIdentifier, callInstance.ClientId, -1);
                        }
                        catch (Exception exDB)
                        {
                            _logger.LogError(exDB, $"EventsManager : ProcessExpiredEvents - Failed to update the Expired state of the Event for Call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        }
                        continue;
                    }


                    //Check to see if the events pulled, that any are in the new state are support Event types - if not, do not process the events and set the events to processed
                   bool newEventsSupport = eventLogList.Any(ev => ev.DatabaseStateId == 1 
                                                                && (Enum.IsDefined(typeof(EventType), ev.eventType) || 
                                                                    Enum.IsDefined(typeof(EventType_AgentAudit), ev.eventType) || 
                                                                    Enum.IsDefined(typeof(EventType_NonSerialized), ev.eventType)
                                                                    )
                                                            );
                    
                    if ( !newEventsSupport)
                    {
                        _logger.LogInformation($"Expired events that are new are not from the supported Event List.  No further processing required for call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        try
                        {
                            await _unitOfWork.SetEventProcessed(callInstance.CallIdentifier, callInstance.ClientId, -1);
                        }
                        catch (Exception exDB)
                        {
                            _logger.LogError(exDB, $"EventsManager : ProcessExpiredEvents - Failed to update the Process state of the Event for Call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        }
                        continue;
                    }

                    RootEvent existingEventRoot = new RootEvent { CallId = callInstance.CallIdentifier};
                    foreach (EventLog eventlog in eventLogList)
                    {
                        existingEventRoot.Events.Add(eventlog);
                    }

                    //Process the call and react as required.
                    _logger.LogWarning($"EventsManager : ProcessExpiredEvents - Processing expired call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                    await ProcessCall(existingEventRoot, callInstance.ClientId, tenantLookup, clientTimeZoneMapping[callInstance.ClientId], -1, true);
                }
            }
        }


        /// <summary>
        /// Retrieves any existing calls that are awaiting processing that are older than the number of minutes.
        /// </summary>
        /// <param name="tenantLookupCollection">Tenant information</param>
        /// /// <param name="clientTimeZoneMapping">Client timezone lookup</param>
        /// <param name="olderthanMinutes">Age of the call in the process queue</param>
        public async Task ProcessQueueCalls(Dictionary<string, Dictionary<string, string>> tenantLookupCollection, Dictionary<string, NodaTime.DateTimeZone> clientTimeZoneMapping,  int olderthanMinutes)
        {
            IList<CallInstance> callLookup;

            try
            {
                callLookup = await _unitOfWork.GetProcessQueueOlderThan(olderthanMinutes);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "EventsManager : ProcessQueueCalls - Failed to retrieve the Call awaiting processing.");
                return;
            }

            if (callLookup != null && callLookup.Count > 0)
            {
                Dictionary<string, string> tenantLookup = null;
                List<EventLog> eventLogList;
                foreach (CallInstance callInstance in callLookup)
                {
                    //Make sure the Tenant lookup is defined for the found Client.
                    if (tenantLookupCollection != null && tenantLookupCollection.ContainsKey(callInstance.ClientId))
                    {
                        tenantLookup = tenantLookupCollection[callInstance.ClientId];
                    }
                    else
                    {
                        _logger.LogError($"EventsManager : ProcessQueueCalls: Could not locate Tenant information for call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        try
                        {
                            await _unitOfWork.SetErrorEventState(callInstance.CallIdentifier, callInstance.ClientId, -1);
                            //Remove the queue record to avoid re-attempted on each cycle.
                            await _unitOfWork.DeleteProcessQueue(callInstance.CallIdentifier, callInstance.ClientId);
                        }
                        catch (Exception exDB)
                        {
                            _logger.LogError(exDB, $"EventsManager : ProcessQueueCalls - Failed to update the Error state of the Event for Call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        }
                        continue;
                    }

                    //Next, make sure the Timezone configuration is available, halting process if it is not.
                    if (!clientTimeZoneMapping.ContainsKey(callInstance.ClientId))
                    {
                        _logger.LogError($"EventsManager: ProcessExpiredEvents - Client Code {callInstance.ClientId} does not have a configured timezone (ref: clientTimezoneMapping settings)");
                        try
                        {
                            await _unitOfWork.SetErrorEventState(callInstance.CallIdentifier, callInstance.ClientId, -1);
                        }
                        catch (Exception exDB)
                        {
                            _logger.LogError(exDB, $"EventsManager : ProcessExpiredEvents - Failed to update the Error state of the Event for Call {callInstance.ClientId}:{callInstance.CallIdentifier}");
                        }
                        continue;
                    }

                    //retrieve all Events associated to the given Call Identifier, this has no bounds on max event Id.
                    eventLogList = await _unitOfWork.GetEventsForProcessing(callInstance.CallIdentifier, callInstance.ClientId, -1);

                    RootEvent existingEventRoot = new RootEvent { CallId = callInstance.CallIdentifier };
                    foreach (EventLog eventlog in eventLogList)
                    {
                        existingEventRoot.Events.Add(eventlog);
                    }

                    //Process the call and react as required.
                    await ProcessCall(existingEventRoot, callInstance.ClientId, tenantLookup, clientTimeZoneMapping[callInstance.ClientId], -1);

                    //Note: always delete from the process queue after processing attempting, as if even it fails, don't want to re-try until debugged/resolved.
                    try
                    {
                        await _unitOfWork.DeleteProcessQueue(callInstance.CallIdentifier, callInstance.ClientId);
                    }
                    catch (Exception exDB)
                    {
                        _logger.LogError(exDB, $"EventsManager : ProcessQueueCalls - Failed to clear Call from Queue {callInstance.ClientId}:{callInstance.CallIdentifier}");
                    }
                }
            }
        }

        public async Task CleanUpTables(int hashEventsOlderThan, int eventsOlderThan, int agentSessionOlderThan)
        {
            try
            {
                ///Originally it was a single DB procedure, however, it could lead to failure on a secondary query or a issue; breaking it out into the individual tables results in the ability to track any issues/load control.

                _logger.LogInformation($"DB Cleanup Executing with timing of (Table: Older Than in Hours) - HashEvent: {hashEventsOlderThan}, Events: {eventsOlderThan}, AgentSession: {agentSessionOlderThan}");
                await _unitOfWork.CleanUpTablesHashEvents(hashEventsOlderThan);
                
                await _unitOfWork.CleanUpTablesEvents(eventsOlderThan);
                
                await _unitOfWork.CleanUpTablesAgentSession(agentSessionOlderThan);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cleanup the Tables");
                return;
            }
        }

        /// <summary>
        /// Clears a specific Event record from the Hashed lookup.
        /// </summary>
        /// <param name="eventData">XML Event data</param>
        /// <param name="clientCode">Client</param>
        /// <returns></returns>
        /// <exception cref="exception"></exception>
        public async Task CleanUpEventHash(string eventData, string clientCode)
        {
            var hashedEvent = new HashingHelper().Hash(eventData);
            await _unitOfWork.DeleteHashEvent(hashedEvent, clientCode);
        }
    }
}
