﻿/** 
	Created: Aug 2021
	Updated: Nov 2023 - reflecting new DB name
	Author: <PERSON>
	Contains any update calls specific to existing tables.  If net new table, it should be in the Creation file.  
**/

INSERT INTO CollectorAPI.processstate(State) VALUES('new');
INSERT INTO CollectorAPI.processstate(State) VALUES('in-process');
INSERT INTO CollectorAPI.processstate(State) VALUES('processed');
INSERT INTO CollectorAPI.processstate(State) VALUES('error');
INSERT INTO CollectorAPI.processstate(State) VALUES('expired');


--create indexes
CREATE INDEX IX_timeStamp ON InsightsData.callsummary (timeStamp);
CREATE INDEX IX_callDetailsIndex ON InsightsData.callsummary (callDetailsIndex);

CREATE INDEX IX_callIdentifier ON InsightsData.callevent (callIdentifier);
CREATE INDEX IX_customerName ON InsightsData.callevent (customerName);
