import * as cdk from 'aws-cdk-lib';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as dynamodb from 'aws-cdk-lib/aws-dynamodb';
import * as rds from 'aws-cdk-lib/aws-rds';
import * as opensearch from 'aws-cdk-lib/aws-opensearch';
import * as s3 from 'aws-cdk-lib/aws-s3';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as cognito from 'aws-cdk-lib/aws-cognito';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as logs from 'aws-cdk-lib/aws-logs';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as stepfunctions from 'aws-cdk-lib/aws-stepfunctions';
import * as sfnTasks from 'aws-cdk-lib/aws-stepfunctions-tasks';
import * as kinesis from 'aws-cdk-lib/aws-kinesis';
import * as kinesisAnalytics from 'aws-cdk-lib/aws-kinesisanalytics';
import * as wafv2 from 'aws-cdk-lib/aws-wafv2';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';

export interface CollectorApiStackProps extends cdk.StackProps {
  environment: string;
  clientConfigs: ClientConfig[];
}

export interface ClientConfig {
  clientId: string;
  clientName: string;
  indexPrefix: string;
  timezone: string;
  tenantMapping: Record<string, string>;
}

export class CollectorApiStack extends cdk.Stack {
  public readonly api: apigateway.RestApi;
  public readonly vpc: ec2.Vpc;
  public readonly openSearchDomain: opensearch.Domain;
  public readonly auroraCluster: rds.ServerlessCluster;

  constructor(scope: Construct, id: string, props: CollectorApiStackProps) {
    super(scope, id, props);

    // VPC for secure networking
    this.vpc = this.createVpc();

    // Security groups
    const securityGroups = this.createSecurityGroups();

    // DynamoDB tables
    const dynamoTables = this.createDynamoTables();

    // Aurora Serverless cluster
    this.auroraCluster = this.createAuroraCluster(securityGroups.aurora);

    // OpenSearch domain
    this.openSearchDomain = this.createOpenSearchDomain(securityGroups.opensearch);

    // S3 buckets
    const s3Buckets = this.createS3Buckets();

    // SQS queues
    const sqsQueues = this.createSqsQueues();

    // SNS topics
    const snsTopics = this.createSnsTopics();

    // Kinesis streams
    const kinesisStreams = this.createKinesisStreams();

    // Secrets Manager
    const secrets = this.createSecrets(props.clientConfigs);

    // Cognito User Pool
    const userPool = this.createCognitoUserPool();

    // Lambda functions
    const lambdaFunctions = this.createLambdaFunctions({
      dynamoTables,
      auroraCluster: this.auroraCluster,
      openSearchDomain: this.openSearchDomain,
      s3Buckets,
      sqsQueues,
      kinesisStreams,
      secrets,
      securityGroups
    });

    // Step Functions
    const stepFunctions = this.createStepFunctions(lambdaFunctions);

    // API Gateway
    this.api = this.createApiGateway(lambdaFunctions, userPool);

    // WAF
    const webAcl = this.createWaf();
    this.associateWafWithApiGateway(webAcl);

    // CloudWatch monitoring
    this.createMonitoring(lambdaFunctions, dynamoTables);

    // Outputs
    this.createOutputs();
  }

  private createVpc(): ec2.Vpc {
    return new ec2.Vpc(this, 'CollectorVpc', {
      maxAzs: 3,
      natGateways: 2,
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'Public',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'Private',
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 24,
          name: 'Isolated',
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
      ],
      enableDnsHostnames: true,
      enableDnsSupport: true,
    });
  }

  private createSecurityGroups() {
    const lambdaSg = new ec2.SecurityGroup(this, 'LambdaSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for Lambda functions',
      allowAllOutbound: true,
    });

    const auroraSg = new ec2.SecurityGroup(this, 'AuroraSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for Aurora cluster',
      allowAllOutbound: false,
    });

    auroraSg.addIngressRule(
      lambdaSg,
      ec2.Port.tcp(3306),
      'Allow Lambda access to Aurora'
    );

    const opensearchSg = new ec2.SecurityGroup(this, 'OpenSearchSecurityGroup', {
      vpc: this.vpc,
      description: 'Security group for OpenSearch cluster',
      allowAllOutbound: false,
    });

    opensearchSg.addIngressRule(
      lambdaSg,
      ec2.Port.tcp(443),
      'Allow Lambda access to OpenSearch'
    );

    return {
      lambda: lambdaSg,
      aurora: auroraSg,
      opensearch: opensearchSg,
    };
  }

  private createDynamoTables() {
    const eventsTable = new dynamodb.Table(this, 'EventsTable', {
      tableName: `collector-events-${this.stackName}`,
      partitionKey: { name: 'PK', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'SK', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.ON_DEMAND,
      encryption: dynamodb.TableEncryption.AWS_MANAGED,
      pointInTimeRecovery: true,
      timeToLiveAttribute: 'ttl',
      removalPolicy: cdk.RemovalPolicy.RETAIN,
    });

    eventsTable.addGlobalSecondaryIndex({
      indexName: 'GSI1',
      partitionKey: { name: 'GSI1PK', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'GSI1SK', type: dynamodb.AttributeType.STRING },
    });

    const eventHashTable = new dynamodb.Table(this, 'EventHashTable', {
      tableName: `collector-event-hash-${this.stackName}`,
      partitionKey: { name: 'PK', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'SK', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.ON_DEMAND,
      encryption: dynamodb.TableEncryption.AWS_MANAGED,
      timeToLiveAttribute: 'ttl',
      removalPolicy: cdk.RemovalPolicy.RETAIN,
    });

    const processQueueTable = new dynamodb.Table(this, 'ProcessQueueTable', {
      tableName: `collector-process-queue-${this.stackName}`,
      partitionKey: { name: 'PK', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'SK', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.ON_DEMAND,
      encryption: dynamodb.TableEncryption.AWS_MANAGED,
      timeToLiveAttribute: 'ttl',
      removalPolicy: cdk.RemovalPolicy.RETAIN,
    });

    const agentSessionTable = new dynamodb.Table(this, 'AgentSessionTable', {
      tableName: `collector-agent-session-${this.stackName}`,
      partitionKey: { name: 'PK', type: dynamodb.AttributeType.STRING },
      sortKey: { name: 'SK', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.ON_DEMAND,
      encryption: dynamodb.TableEncryption.AWS_MANAGED,
      timeToLiveAttribute: 'ttl',
      removalPolicy: cdk.RemovalPolicy.RETAIN,
    });

    return {
      events: eventsTable,
      eventHash: eventHashTable,
      processQueue: processQueueTable,
      agentSession: agentSessionTable,
    };
  }

  private createAuroraCluster(securityGroup: ec2.SecurityGroup): rds.ServerlessCluster {
    const subnetGroup = new rds.SubnetGroup(this, 'AuroraSubnetGroup', {
      vpc: this.vpc,
      description: 'Subnet group for Aurora cluster',
      vpcSubnets: {
        subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
      },
    });

    return new rds.ServerlessCluster(this, 'AuroraCluster', {
      engine: rds.DatabaseClusterEngine.auroraMysql({
        version: rds.AuroraMysqlEngineVersion.VER_8_0_MYSQL_3_02_0,
      }),
      vpc: this.vpc,
      securityGroups: [securityGroup],
      subnetGroup,
      scaling: {
        autoPause: cdk.Duration.minutes(10),
        minCapacity: rds.AuroraCapacityUnit.ACU_0_5,
        maxCapacity: rds.AuroraCapacityUnit.ACU_16,
      },
      enableDataApi: true,
      deletionProtection: true,
      backupRetention: cdk.Duration.days(7),
      removalPolicy: cdk.RemovalPolicy.RETAIN,
    });
  }

  private createOpenSearchDomain(securityGroup: ec2.SecurityGroup): opensearch.Domain {
    return new opensearch.Domain(this, 'OpenSearchDomain', {
      version: opensearch.EngineVersion.OPENSEARCH_2_3,
      capacity: {
        masterNodes: 3,
        masterNodeInstanceType: 'r6g.medium.search',
        dataNodes: 3,
        dataNodeInstanceType: 'r6g.large.search',
        warmNodes: 2,
        warmInstanceType: 'r6g.medium.search',
      },
      ebs: {
        volumeSize: 100,
        volumeType: ec2.EbsDeviceVolumeType.GP3,
      },
      zoneAwareness: {
        enabled: true,
        availabilityZoneCount: 3,
      },
      vpc: this.vpc,
      vpcSubnets: [
        {
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
      ],
      securityGroups: [securityGroup],
      encryptionAtRest: {
        enabled: true,
      },
      nodeToNodeEncryption: true,
      enforceHttps: true,
      removalPolicy: cdk.RemovalPolicy.RETAIN,
    });
  }

  private createS3Buckets() {
    const eventArchiveBucket = new s3.Bucket(this, 'EventArchiveBucket', {
      bucketName: `collector-event-archive-${this.account}-${this.region}`,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      versioned: true,
      lifecycleRules: [
        {
          id: 'ArchiveRule',
          enabled: true,
          transitions: [
            {
              storageClass: s3.StorageClass.INFREQUENT_ACCESS,
              transitionAfter: cdk.Duration.days(30),
            },
            {
              storageClass: s3.StorageClass.GLACIER,
              transitionAfter: cdk.Duration.days(90),
            },
            {
              storageClass: s3.StorageClass.DEEP_ARCHIVE,
              transitionAfter: cdk.Duration.days(365),
            },
          ],
        },
      ],
      removalPolicy: cdk.RemovalPolicy.RETAIN,
    });

    const configBucket = new s3.Bucket(this, 'ConfigBucket', {
      bucketName: `collector-config-${this.account}-${this.region}`,
      encryption: s3.BucketEncryption.S3_MANAGED,
      blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
      versioned: true,
      removalPolicy: cdk.RemovalPolicy.RETAIN,
    });

    return {
      eventArchive: eventArchiveBucket,
      config: configBucket,
    };
  }

  private createSqsQueues() {
    const eventProcessingQueue = new sqs.Queue(this, 'EventProcessingQueue', {
      queueName: `collector-event-processing-${this.stackName}`,
      visibilityTimeout: cdk.Duration.minutes(5),
      encryption: sqs.QueueEncryption.SQS_MANAGED,
      deadLetterQueue: {
        queue: new sqs.Queue(this, 'EventProcessingDLQ', {
          queueName: `collector-event-processing-dlq-${this.stackName}`,
          encryption: sqs.QueueEncryption.SQS_MANAGED,
        }),
        maxReceiveCount: 3,
      },
    });

    return {
      eventProcessing: eventProcessingQueue,
    };
  }

  private createSnsTopics() {
    const alertsTopic = new sns.Topic(this, 'AlertsTopic', {
      topicName: `collector-alerts-${this.stackName}`,
      encryption: sns.TopicEncryption.SQS_MANAGED,
    });

    return {
      alerts: alertsTopic,
    };
  }

  private createKinesisStreams() {
    const eventStream = new kinesis.Stream(this, 'EventStream', {
      streamName: `collector-events-${this.stackName}`,
      streamMode: kinesis.StreamMode.ON_DEMAND,
      encryption: kinesis.StreamEncryption.MANAGED,
      retentionPeriod: cdk.Duration.hours(168), // 7 days
    });

    return {
      events: eventStream,
    };
  }
