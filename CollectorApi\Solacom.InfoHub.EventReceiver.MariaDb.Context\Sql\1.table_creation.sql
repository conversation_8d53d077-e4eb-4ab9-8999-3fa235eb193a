/** 
	Created: Aug 2021
	Updated: Nov 2023 - SPRI updates to remove unused / reset based on new DB structure.
	Author: <PERSON>
	Generates the tables if they do not exists.  Using the IF NOT EXISTS syntax, so warning will appear if they already exist
**/

DELIMITER $$

-- CollectorAPI.processstate definition
CREATE TABLE IF NOT EXISTS CollectorAPI.`processstate` (
  `id` int NOT NULL AUTO_INCREMENT,
  `State` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ProcessState_un` (`State`)
) ENGINE=InnoDB;
$$

-- CollectorAPI.events definition
CREATE TABLE IF NOT EXISTS CollectorAPI.`events` (
  `id` int NOT NULL AUTO_INCREMENT,
  `Client_Id` varchar(100) NOT NULL,
  `Call_Identifier` varchar(256) DEFAULT NULL,
  `EventType` varchar(128) NOT NULL,
  `Event_Data` json NOT NULL,
  `Date_Created` datetime NOT NULL,
  `State_Id` int NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `Event_FK` (`State_Id`),
  CONSTRAINT `Event_FK` FOREIGN KEY (`State_Id`) REFERENCES `processstate` (`id`)
) ENGINE=InnoDB;
$$

-- guardianinsights.agentsession definition
CREATE TABLE IF NOT EXISTS CollectorAPI.`agentsession` (
  `id` int NOT NULL AUTO_INCREMENT,
  `Client_Id` varchar(100) NOT NULL,
  `MediaLabel` varchar(512) NOT NULL,
  `Agent_Data` json NOT NULL,
  `Date_Created` datetime NOT NULL,
  `Date_Updated` datetime NOT NULL,
  `State_Id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `AgentSession_FK` (`State_Id`),
  CONSTRAINT `AgentSession_FK` FOREIGN KEY (`State_Id`) REFERENCES `processstate` (`id`)
) ENGINE=InnoDB;
$$

-- CollectorAPI.hashedevents definition
CREATE TABLE IF NOT EXISTS CollectorAPI.`hashedevents` (
  `id` int NOT NULL AUTO_INCREMENT,
  `Client_Id` varchar(100) NOT NULL,
  `hashed_key` varchar(512) NOT NULL,
  `hashed_data` json NOT NULL,
  `Date_Created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `hashedevents_un` (`Client_Id`,`hashed_key`)
) ENGINE=InnoDB;
$$


-- guardianinsights.processerror definition
CREATE TABLE IF NOT EXISTS CollectorAPI.`processerror` (
  `id` int NOT NULL AUTO_INCREMENT,
  `Client_Id` varchar(100) DEFAULT NULL,
  `Call_Identifier` varchar(256) DEFAULT NULL,
  `Error_Message` varchar(100) NOT NULL,
  `Date_Created` datetime NOT NULL,
  `State_Id` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `ProcessError_FK` (`State_Id`),
  CONSTRAINT `ProcessError_FK` FOREIGN KEY (`State_Id`) REFERENCES `processstate` (`id`)
) ENGINE=InnoDB;
$$


-- InsightsData.callsummary definition
CREATE TABLE IF NOT EXISTS InsightsData.`callsummary` (
	`id` int NOT NULL AUTO_INCREMENT,
	`CustomerName` varchar(100) NOT NULL,
	`tenantPsapName` varchar(100) NOT NULL,
	`callIdentifier` nvarchar(100),
	`callDetailsIndex` int,
	`isAbandonedState` SMALLINT,
	`address` nvarchar(256),
	`isAdminCall`	SMALLINT,
	`isAdminEmergencyCall` SMALLINT,
	`agentAnsweredWithin10s` SMALLINT,
	`agentAnsweredWithin15s` SMALLINT,
	`agentAnsweredWithin20s` SMALLINT,
	`agentAnsweredWithin40s` SMALLINT,
	`agentAnsweredMoreThan10s` SMALLINT,
	`agentAnsweredMoreThan20s` SMALLINT,
	`agentAnsweredMoreThan40s` SMALLINT,
	`agentCallbacknumber` nvarchar(50),
	`agentName`	nvarchar(100),
	`agentTimeToAnswerInSeconds` double,
	`answeredBySystem` SMALLINT,
	`systemAnsweredWithin10s` smallint,
	`systemAnsweredWithin15s` smallint,
	`systemAnsweredWithin20s` smallint,
	`systemAnsweredWithin40s` smallint,
	`systemAnsweredMoreThan10s` smallint,
	`systemAnsweredMoreThan20s` smallint,
	`systemAnsweredMoreThan40s` smallint,
	`nonEmergencyAnsweredWithin10s` smallint,
	`nonEmergencyAnsweredWithin15s` smallint,
	`nonEmergencyAnsweredWithin20s` smallint,
	`nonEmergencyAnsweredWithin40s` smallint,
	`nonEmergencyAnsweredMoreThan10s` smallint,
	`nonEmergencyAnsweredMoreThan20s` smallint,
	`nonEmergencyAnsweredMoreThan40s` smallint,
	`callAnswered` datetime(4),
	`callAnsweredToLocal` datetime(4),
	`callArrivedSystem` datetime(4),
	`callArrivedSystemToLocal` datetime(4),
	`callPresented` datetime(4),
	`callPresentedToLocal` datetime(4),
	`callReleased` datetime(4),
	`callReleasedToLocal` datetime(4),
	`callTransferred` datetime(4),
	`callTransferredToLocal` datetime(4),
	`callBackNumber` nvarchar(50),
	`callMobilityType` nvarchar(50),
	`callState` nvarchar(50),
	`callType` nvarchar(50),
	`carrier` nvarchar(50),
	`isCompleted` smallint,
	`confidence`  double,
	`isEmergencyCall` smallint,
	`endCallTime` datetime(4),
	`endCallTimeToLocal` datetime(4),
	`esn`  double,
	`finalCos` nvarchar(50),
	`holdTimeInSeconds`  double,
	`isInProgress` smallint,
	`incidentIdentifier` nvarchar(100),
	`isAbandoned` smallint,
	`isAbandonedCallback` smallint,
	`isAdmin` smallint,
	`isAdminEmergency` smallint,
	`isCallback` smallint,
	`isEmergency` smallint,
	`isInternalTransferCall` smallint,
	`isTransferred` smallint,
	`isAlternativeRoute` smallint,
	`isOutbound` smallint,
	`isTandem` smallint,
	`isTandemCall` smallint,
	`isUnknownCall` smallint,
	`isTDDChallenge` smallint,
	`isTDDType` smallint,
	`isVoipType` smallint,
	`isWirelessType` smallint,
	`isUnknownType` smallint,
	`isNotFoundType` smallint,
	`isLandlineType` smallint,
	`isRTTType` smallint,
	`isSMSType` smallint,
	`latitude` double,
	`longitude` double,
	`mediaLabel` nvarchar(100),
	`nonEmergencyHoldTimeInSeconds` double,
	`nonEmergencyPsapTimeToAnswerInSeconds` double,
	`originalCos` nvarchar(50),
	`processedTime` datetime(4),
	`psapName` nvarchar(100),
	`psapTimeToAnswerInSeconds` double,
	`startCallTime` datetime(4),
	`startCallTimeToLocal` datetime(4),
	`talkTimeInSeconds` double,
	`nonEmergencyTalkTimeInSeconds` double,
	`timeStamp` datetime(4),
	`timeStampToLocal` datetime(4),
	`timeToAnswerInSeconds` double,
	`nonEmergencyTimeToAnswerInSeconds` double,
	`timeToTransferInSeconds` double,
	`totalCallTimeInSeconds` double,
	`nonEmergencyTotalCallTimeInSeconds` double,
	`transferFrom` nvarchar(100),
	`transferTo` nvarchar(100),
	`uncertainty` double,
	`zipcode`  nvarchar(50),  
	`locationDataList` JSON,
	`dateCreated` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;
$$

-- InsightsData.events definition
CREATE TABLE IF NOT EXISTS InsightsData.`callevent` (
  `id` int NOT NULL AUTO_INCREMENT,
  `customerName` varchar(100) NOT NULL,
  `tenantPsapName` varchar(100) NULL,
  `callIdentifier` varchar(256) DEFAULT NULL,
  `eventType` varchar(50) NOT NULL,
  `eventDatetime` datetime(4) NOT NULL,
  `eventDatetimeToLocal` datetime(4) NOT NULL,
  `eventData` json NOT NULL,
  `eventReceived` datetime(4) NOT NULL,
  `dateCreated` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB;
$$

CREATE TABLE IF NOT EXISTS CollectorAPI.`processqueue` (
  `id` int NOT NULL AUTO_INCREMENT,
  `callIdentifier` varchar(256) NOT NULL,
  `clientId` varchar(100) NOT NULL,
  `dateCreated` datetime NOT NULL,
  `dateUpdated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `processqueue_un` (`clientId`,`callIdentifier`)
) ENGINE=InnoDB;
$$
